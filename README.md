# Express Plus

A modular, type-safe Express.js wrapper with automatic OpenAPI documentation generation, built using the adapter pattern for maximum flexibility.

## 🚀 Features

- **Modular Architecture**: Swap between different web frameworks and validation libraries
- **Type Safety**: Full TypeScript support with automatic type inference
- **Automatic OpenAPI Generation**: Generate OpenAPI 3.0 specifications from your route definitions
- **Validation**: Built-in request validation with detailed error messages
- **Adapter Pattern**: Easy to extend with new frameworks and validation libraries
- **Production Ready**: Comprehensive error handling and configuration options

## 📦 Installation

```bash
npm install express_plus
```

## 🏗️ Architecture

Express Plus uses the **Adapter Pattern** to provide a consistent API while allowing you to swap underlying implementations:

```
┌─────────────────────────────────────────┐
│           Express Plus Library          │
├─────────────────────────────────────────┤
│  ┌─────────────────┐ ┌─────────────────┐ │
│  │ Framework       │ │ Validation      │ │
│  │ Adapter         │ │ Adapter         │ │
│  │ Interface       │ │ Interface       │ │
│  └─────────────────┘ └─────────────────┘ │
├─────────────────────────────────────────┤
│  ┌─────────────────┐ ┌─────────────────┐ │
│  │ Express         │ │ Zod             │ │
│  │ Adapter         │ │ Adapter         │ │
│  └─────────────────┘ └─────────────────┘ │
│  ┌─────────────────┐ ┌─────────────────┐ │
│  │ Fastify         │ │ Joi             │ │
│  │ Adapter         │ │ Adapter         │ │
│  │ (Future)        │ │ (Custom)        │ │
│  └─────────────────┘ └─────────────────┘ │
└─────────────────────────────────────────┘
```

## 🚀 Quick Start

```typescript
import { z } from 'zod';
import { 
  ExpressPlusLibrary, 
  ExpressAdapter, 
  ZodAdapter, 
  defineHandler 
} from 'express_plus';

// Initialize library with adapters
const library = new ExpressPlusLibrary();
library.setFrameworkAdapter(new ExpressAdapter());
library.setValidationAdapter(new ZodAdapter());

// Define a handler with validation
const createUser = defineHandler({
  body: z.object({
    name: z.string().min(1),
    email: z.string().email(),
    age: z.number().min(18)
  }),
  response: z.object({
    id: z.string(),
    name: z.string(),
    email: z.string(),
    age: z.number()
  }),
  handler: async ({ body }) => {
    return {
      id: Math.random().toString(36),
      ...body
    };
  }
});

// Register the route
library.defineRoute('post', '/users', createUser);

// Setup error handling and OpenAPI
library.setupErrorHandling();
library.addOpenAPIEndpoint('/docs/openapi.json');

// Start server
library.listen(3000, () => {
  console.log('Server running on port 3000');
});
```

## 📚 Core Concepts

### Adapters

Express Plus uses adapters to abstract away framework and validation library specifics:

#### Framework Adapters
- **ExpressAdapter**: Express.js implementation
- **Future**: Fastify, Hono, Koa adapters

#### Validation Adapters  
- **ZodAdapter**: Zod schema validation
- **Custom**: Easy to implement custom validation adapters

### Handler Definition

Use `defineHandler` to create type-safe handlers with validation:

```typescript
const handler = defineHandler({
  // Request validation schemas
  body: z.object({ /* ... */ }),
  query: z.object({ /* ... */ }),
  params: z.object({ /* ... */ }),
  
  // Response schema (for OpenAPI)
  response: z.object({ /* ... */ }),
  
  // Metadata for documentation
  metadata: {
    name: 'Create User',
    description: 'Creates a new user',
    tags: ['users']
  },
  
  // Handler function
  handler: async ({ body, query, params }) => {
    // Your logic here
    return result;
  }
});
```

### Route Registration

Register routes with the library:

```typescript
library.defineRoute('post', '/users', handler);
library.defineRoute('get', '/users/:id', getUserHandler);
```

## 🔧 Configuration

Configure the library with various options:

```typescript
const library = new ExpressPlusLibrary({
  openapi: {
    title: 'My API',
    version: '1.0.0',
    description: 'API description'
  },
  validation: {
    stripUnknown: true,
    abortEarly: false
  },
  errorHandling: {
    includeStack: process.env.NODE_ENV === 'development'
  }
});
```

## 🔌 Creating Custom Adapters

### Custom Validation Adapter

```typescript
import { IValidationAdapter, ValidationError } from 'express_plus';

class MyValidationAdapter implements IValidationAdapter<MySchema> {
  validate(data: any, schema: MySchema): any {
    // Implement validation logic
  }
  
  parseValidationError(error: any): ValidationError[] {
    // Convert errors to standard format
  }
  
  schemaToOpenAPI(schema: MySchema): any {
    // Convert schema to OpenAPI format
  }
  
  isValidSchema(schema: any): schema is MySchema {
    // Check if schema is valid
  }
  
  getName(): string {
    return 'my-validator';
  }
}
```

### Custom Framework Adapter

```typescript
import { IFrameworkAdapter } from 'express_plus';

class MyFrameworkAdapter implements IFrameworkAdapter {
  registerRoute(method, path, handler, middleware) {
    // Register route with your framework
  }
  
  createValidationMiddleware(schema, validationAdapter) {
    // Create validation middleware
  }
  
  // Implement other required methods...
}
```

## 📖 Examples

### Basic Usage
```bash
npm run example:basic
```

### Adapter Pattern Demo
```bash
npm run example:adapters
```

## 🧪 Testing

```bash
npm test
```

## 📄 API Documentation

The library automatically generates OpenAPI 3.0 documentation. Access it at:
- `/docs/openapi.json` - OpenAPI specification

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📝 License

MIT License - see LICENSE file for details.

## 🔮 Roadmap

- [ ] Fastify adapter
- [ ] Hono adapter  
- [ ] Additional validation adapters (Yup, Ajv)
- [ ] Swagger UI integration
- [ ] Rate limiting middleware
- [ ] Authentication middleware
- [ ] Response caching
- [ ] Request/response transformation hooks
