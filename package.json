{"name": "express_plus", "version": "0.0.1", "description": "Express Plus - Express wrapper to integrate with OpenAPI docs generator", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "nodemon --exec ts-node src/index.ts", "dev:watch": "nodemon --watch src --ext ts --exec ts-node src/index.ts", "clean": "rm -rf dist", "prebuild": "npm run clean", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "example:basic": "ts-node examples/basic-usage.ts", "example:adapters": "ts-node examples/adapter-pattern-demo.ts"}, "keywords": ["express", "openapi", "typescript"], "author": "SeifAlmotaz", "license": "MIT", "devDependencies": {"@types/express": "^5.0.3", "@types/jest": "^30.0.0", "@types/node": "^24.0.4", "@types/supertest": "^6.0.3", "jest": "^29.7.0", "nodemon": "^3.1.10", "supertest": "^7.1.1", "ts-jest": "^29.4.0", "ts-node": "^10.9.2", "typescript": "^5.8.3"}, "dependencies": {"@fastify/cors": "^11.0.1", "@types/joi": "^17.2.2", "express": "^5.1.0", "fastify": "^5.4.0", "joi": "^17.13.3", "zod": "^3.25.67"}, "jest": {"preset": "ts-jest", "testEnvironment": "node", "roots": ["<rootDir>/src", "<rootDir>/tests"], "testMatch": ["**/__tests__/**/*.ts", "**/?(*.)+(spec|test).ts"], "collectCoverageFrom": ["src/**/*.ts", "!src/**/*.d.ts", "!src/index.ts"]}}