"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.defineHandler = defineHandler;
exports.withSchema = withSchema;
exports.withMetadata = withMetadata;
exports.addMetadata = addMetadata;
exports.addSchema = addSchema;
exports.getHandlerSchema = getHandlerSchema;
exports.getHandlerMetadata = getHandlerMetadata;
exports.isEnhancedHandler = isEnhancedHandler;
exports.cloneHandler = cloneHandler;
exports.composeHandlers = composeHandlers;
exports.createTransformHandler = createTransformHandler;
function defineHandler(options) {
    const { handler, metadata, ...schema } = options;
    const enhancedHandler = handler;
    enhancedHandler.__schema = schema;
    if (metadata) {
        enhancedHandler.__metadata = metadata;
    }
    return enhancedHandler;
}
function withSchema(schema, handler) {
    return defineHandler({ ...schema, handler });
}
function withMetadata(metadata, handler) {
    return defineHandler({ handler, metadata });
}
function addMetadata(handler, metadata) {
    const newHandler = handler;
    newHandler.__metadata = { ...newHandler.__metadata, ...metadata };
    return newHandler;
}
function addSchema(handler, schema) {
    const newHandler = handler;
    newHandler.__schema = { ...newHandler.__schema, ...schema };
    return newHandler;
}
function getHandlerSchema(handler) {
    return handler.__schema;
}
function getHandlerMetadata(handler) {
    return handler.__metadata;
}
function isEnhancedHandler(handler) {
    return typeof handler === 'function' && !!(handler.__schema || handler.__metadata);
}
function cloneHandler(handler, modifications) {
    const newHandler = (modifications?.handler || handler);
    if (handler.__schema || modifications?.schema) {
        newHandler.__schema = { ...handler.__schema, ...modifications?.schema };
    }
    if (handler.__metadata || modifications?.metadata) {
        newHandler.__metadata = { ...handler.__metadata, ...modifications?.metadata };
    }
    return newHandler;
}
function composeHandlers(...handlers) {
    if (handlers.length === 0) {
        throw new Error('At least one handler must be provided');
    }
    if (handlers.length === 1) {
        const handler = handlers[0];
        return isEnhancedHandler(handler) ? handler : defineHandler({ handler });
    }
    let mergedSchema = {};
    let mergedMetadata = {};
    for (const handler of handlers) {
        if (isEnhancedHandler(handler)) {
            if (handler.__schema) {
                mergedSchema = { ...mergedSchema, ...handler.__schema };
            }
            if (handler.__metadata) {
                mergedMetadata = { ...mergedMetadata, ...handler.__metadata };
            }
        }
    }
    const composedHandler = async (validated, context) => {
        let result;
        for (const handler of handlers) {
            result = await handler(validated, context);
            if (result !== undefined) {
                break;
            }
        }
        return result;
    };
    return defineHandler({
        ...mergedSchema,
        handler: composedHandler,
        metadata: Object.keys(mergedMetadata).length > 0 ? mergedMetadata : undefined
    });
}
function createTransformHandler(inputSchema, transform, metadata) {
    const handler = async (validated) => {
        return await transform(validated);
    };
    return defineHandler({
        ...inputSchema,
        handler,
        metadata
    });
}
//# sourceMappingURL=define-handler.js.map