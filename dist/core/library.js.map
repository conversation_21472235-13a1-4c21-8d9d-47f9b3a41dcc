{"version": 3, "file": "library.js", "sourceRoot": "", "sources": ["../../src/core/library.ts"], "names": [], "mappings": ";;;AA0BA,sCAmBC;AAxBD,2DAAuD;AAKvD,SAAgB,aAAa,CAA2D,OAWvF;IACC,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG,MAAM,EAAE,GAAG,OAAO,CAAC;IAEjD,MAAM,eAAe,GAAG,OAA0D,CAAC;IACnF,eAAe,CAAC,QAAQ,GAAG,MAAM,CAAC;IAClC,eAAe,CAAC,UAAU,GAAG,QAAQ,CAAC;IAEtC,OAAO,eAAe,CAAC;AACzB,CAAC;AAKD,MAAa,kBAAkB;IAM7B,YAAY,MAAsB;QAChC,IAAI,CAAC,MAAM,GAAG;YACZ,OAAO,EAAE;gBACP,KAAK,EAAE,kBAAkB;gBACzB,OAAO,EAAE,OAAO;gBAChB,WAAW,EAAE,iCAAiC;gBAC9C,GAAG,MAAM,EAAE,OAAO;aACnB;YACD,UAAU,EAAE;gBACV,YAAY,EAAE,IAAI;gBAClB,UAAU,EAAE,KAAK;gBACjB,GAAG,MAAM,EAAE,UAAU;aACtB;YACD,aAAa,EAAE;gBACb,YAAY,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa;gBACpD,GAAG,MAAM,EAAE,aAAa;aACzB;SACF,CAAC;QAEF,IAAI,CAAC,gBAAgB,GAAG,IAAI,oCAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;IACpE,CAAC;IAKD,mBAAmB,CAAC,OAA0B;QAC5C,IAAI,CAAC,gBAAgB,GAAG,OAAO,CAAC;IAClC,CAAC;IAKD,oBAAoB,CAAC,OAA2B;QAC9C,IAAI,CAAC,iBAAiB,GAAG,OAAO,CAAC;IACnC,CAAC;IAKD,WAAW,CACT,MAAkB,EAClB,IAAY,EACZ,OAAwB,EACxB,MAAoB,EACpB,OAOC;QAED,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC3B,MAAM,IAAI,KAAK,CAAC,8DAA8D,CAAC,CAAC;QAClF,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAC5B,MAAM,IAAI,KAAK,CAAC,gEAAgE,CAAC,CAAC;QACpF,CAAC;QAGD,MAAM,WAAW,GAAG,MAAM,IAAK,OAA2B,CAAC,QAAQ,IAAI,EAAE,CAAC;QAG1E,MAAM,UAAU,GAAG,OAAO,EAAE,UAAU,IAAI,EAAE,CAAC;QAC7C,IAAI,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACxC,MAAM,oBAAoB,GAAG,IAAI,CAAC,gBAAgB,CAAC,0BAA0B,CAC3E,WAAW,EACX,IAAI,CAAC,iBAAiB,CACvB,CAAC;YACF,UAAU,CAAC,OAAO,CAAC,oBAAoB,CAAC,CAAC;QAC3C,CAAC;QAGD,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC;QAGvE,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAC5B,IAAI,EACJ,MAAM,EACN,WAAW,EACX,IAAI,CAAC,iBAAiB,EACtB,OAAO,EAAE,QAAQ,CAClB,CAAC;IACJ,CAAC;IAKD,cAAc;QACZ,OAAO,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC;IACzC,CAAC;IAKD,oBAAoB;QAClB,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC3B,MAAM,IAAI,KAAK,CAAC,8DAA8D,CAAC,CAAC;QAClF,CAAC;QACD,OAAO,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE,CAAC;IAC7C,CAAC;IAKD,kBAAkB,CAAC,aAA6C;QAC9D,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC3B,MAAM,IAAI,KAAK,CAAC,8DAA8D,CAAC,CAAC;QAClF,CAAC;QAED,MAAM,YAAY,GAAG,IAAI,CAAC,gBAAgB,CAAC,kBAAkB,CAC3D,aAAa,IAAI,IAAI,CAAC,yBAAyB,EAAE,CAClD,CAAC;QAGF,MAAM,GAAG,GAAG,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE,CAAC;QAChD,IAAI,GAAG,IAAI,OAAO,GAAG,CAAC,GAAG,KAAK,UAAU,EAAE,CAAC;YACzC,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;QACxB,CAAC;IACH,CAAC;IAKO,yBAAyB;QAC/B,OAAO,CAAC,KAAU,EAAiB,EAAE;YACnC,MAAM,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,YAAY,IAAI,KAAK,CAAC;YAGtE,MAAM,aAAa,GAAkB;gBACnC,KAAK,EAAE,uBAAuB;gBAC9B,OAAO,EAAE,YAAY,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,sBAAsB;gBAC9D,UAAU,EAAE,GAAG;aAChB,CAAC;YAGF,IAAI,YAAY,IAAI,KAAK,CAAC,KAAK,EAAE,CAAC;gBAC/B,aAAqB,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;YAC7C,CAAC;YAED,OAAO,aAAa,CAAC;QACvB,CAAC,CAAC;IACJ,CAAC;IAKD,kBAAkB,CAAC,OAAe,oBAAoB;QACpD,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC3B,MAAM,IAAI,KAAK,CAAC,8DAA8D,CAAC,CAAC;QAClF,CAAC;QAED,MAAM,OAAO,GAAoB,GAAG,EAAE;YACpC,OAAO,IAAI,CAAC,cAAc,EAAE,CAAC;QAC/B,CAAC,CAAC;QAEF,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC,KAAK,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;IAC5D,CAAC;IAKD,MAAM,CAAC,IAAY,EAAE,QAAqB;QACxC,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC3B,MAAM,IAAI,KAAK,CAAC,8DAA8D,CAAC,CAAC;QAClF,CAAC;QAED,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;IAC/C,CAAC;IAKD,SAAS;QACP,OAAO,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;IAC5B,CAAC;IAKD,YAAY,CAAC,MAA8B;QACzC,IAAI,CAAC,MAAM,GAAG;YACZ,GAAG,IAAI,CAAC,MAAM;YACd,GAAG,MAAM;YACT,OAAO,EAAE,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,GAAG,MAAM,CAAC,OAAO,EAAE;YACtD,UAAU,EAAE,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,GAAG,MAAM,CAAC,UAAU,EAAE;YAC/D,aAAa,EAAE,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,GAAG,MAAM,CAAC,aAAa,EAAE;SACzE,CAAC;QAGF,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;YACnB,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QACnD,CAAC;IACH,CAAC;CACF;AA1MD,gDA0MC"}