export type HttpMethod = 'get' | 'post' | 'put' | 'patch' | 'delete' | 'head' | 'options';
export interface RequestData {
    body?: any;
    query?: any;
    params?: any;
    headers?: Record<string, string>;
}
export interface ResponseData {
    status?: number;
    body?: any;
    headers?: Record<string, string>;
}
export interface RouteSchema<TSchema = any> {
    body?: TSchema;
    query?: TSchema;
    params?: TSchema;
    response?: TSchema;
}
export type HandlerFunction<TValidated = RequestData, TResponse = any> = (validated: TValidated, context?: any) => Promise<TResponse> | TResponse;
export interface EnhancedHandler<TSchema = any, TValidated = RequestData, TResponse = any> extends HandlerFunction<TValidated, TResponse> {
    __schema?: RouteSchema<TSchema>;
    __metadata?: {
        name?: string;
        description?: string;
        tags?: string[];
    };
}
export interface ValidationError {
    field: string;
    message: string;
    value?: any;
    path?: string[];
}
export interface ErrorResponse {
    error: string;
    message: string;
    details?: ValidationError[];
    statusCode?: number;
}
export interface OpenAPISpec {
    openapi: string;
    info: {
        title: string;
        version: string;
        description?: string;
    };
    paths: Record<string, any>;
    components?: {
        schemas?: Record<string, any>;
        responses?: Record<string, any>;
        parameters?: Record<string, any>;
    };
}
export interface RouteOptions<TSchema = any> {
    method: HttpMethod;
    path: string;
    handler: EnhancedHandler<TSchema>;
    schema?: RouteSchema<TSchema>;
    middleware?: any[];
    metadata?: {
        name?: string;
        description?: string;
        tags?: string[];
    };
}
export interface LibraryConfig {
    openapi?: {
        title?: string;
        version?: string;
        description?: string;
    };
    validation?: {
        stripUnknown?: boolean;
        abortEarly?: boolean;
    };
    errorHandling?: {
        includeStack?: boolean;
        customErrorHandler?: (error: any) => ErrorResponse;
    };
}
//# sourceMappingURL=types.d.ts.map