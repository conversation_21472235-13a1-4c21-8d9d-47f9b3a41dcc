"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.OpenAPIGenerator = void 0;
class OpenAPIGenerator {
    constructor(info) {
        this.spec = {
            openapi: '3.0.0',
            info: {
                title: info?.title || 'Express Plus API',
                version: info?.version || '1.0.0',
                description: info?.description || 'API generated with Express Plus'
            },
            paths: {},
            components: {
                schemas: {},
                responses: {},
                parameters: {}
            }
        };
    }
    addRoute(path, method, schema, validationAdapter, metadata) {
        if (!this.spec.paths[path]) {
            this.spec.paths[path] = {};
        }
        const operation = {
            summary: metadata?.name || `${method.toUpperCase()} ${path}`,
            description: metadata?.description || `${method.toUpperCase()} operation for ${path}`,
            responses: {
                '200': {
                    description: 'Success'
                },
                '400': {
                    description: 'Bad Request',
                    content: {
                        'application/json': {
                            schema: {
                                type: 'object',
                                properties: {
                                    error: { type: 'string' },
                                    message: { type: 'string' },
                                    details: {
                                        type: 'array',
                                        items: {
                                            type: 'object',
                                            properties: {
                                                field: { type: 'string' },
                                                message: { type: 'string' },
                                                value: {},
                                                path: {
                                                    type: 'array',
                                                    items: { type: 'string' }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                },
                '500': {
                    description: 'Internal Server Error',
                    content: {
                        'application/json': {
                            schema: {
                                type: 'object',
                                properties: {
                                    error: { type: 'string' },
                                    message: { type: 'string' }
                                }
                            }
                        }
                    }
                }
            }
        };
        if (metadata?.tags && metadata.tags.length > 0) {
            operation.tags = metadata.tags;
        }
        if (schema.body && validationAdapter.isValidSchema(schema.body)) {
            operation.requestBody = {
                required: true,
                content: {
                    'application/json': {
                        schema: validationAdapter.schemaToOpenAPI(schema.body)
                    }
                }
            };
        }
        operation.parameters = [];
        if (schema.query && validationAdapter.isValidSchema(schema.query)) {
            const querySchema = validationAdapter.schemaToOpenAPI(schema.query);
            if (querySchema.type === 'object' && querySchema.properties) {
                for (const [paramName, paramSchema] of Object.entries(querySchema.properties)) {
                    operation.parameters.push({
                        name: paramName,
                        in: 'query',
                        required: querySchema.required?.includes(paramName) || false,
                        schema: paramSchema
                    });
                }
            }
        }
        if (schema.params && validationAdapter.isValidSchema(schema.params)) {
            const paramsSchema = validationAdapter.schemaToOpenAPI(schema.params);
            if (paramsSchema.type === 'object' && paramsSchema.properties) {
                for (const [paramName, paramSchema] of Object.entries(paramsSchema.properties)) {
                    operation.parameters.push({
                        name: paramName,
                        in: 'path',
                        required: true,
                        schema: paramSchema
                    });
                }
            }
        }
        if (schema.response && validationAdapter.isValidSchema(schema.response)) {
            operation.responses['200'] = {
                description: 'Success',
                content: {
                    'application/json': {
                        schema: validationAdapter.schemaToOpenAPI(schema.response)
                    }
                }
            };
        }
        if (operation.parameters.length === 0) {
            delete operation.parameters;
        }
        this.spec.paths[path][method] = operation;
    }
    getSpec() {
        return JSON.parse(JSON.stringify(this.spec));
    }
    updateInfo(info) {
        if (info.title)
            this.spec.info.title = info.title;
        if (info.version)
            this.spec.info.version = info.version;
        if (info.description)
            this.spec.info.description = info.description;
    }
    clear() {
        this.spec.paths = {};
        if (this.spec.components) {
            this.spec.components.schemas = {};
            this.spec.components.responses = {};
            this.spec.components.parameters = {};
        }
    }
    addSchemaComponent(name, schema) {
        if (!this.spec.components) {
            this.spec.components = {};
        }
        if (!this.spec.components.schemas) {
            this.spec.components.schemas = {};
        }
        this.spec.components.schemas[name] = schema;
    }
    getSchemaReference(name) {
        return { $ref: `#/components/schemas/${name}` };
    }
}
exports.OpenAPIGenerator = OpenAPIGenerator;
//# sourceMappingURL=openapi-generator.js.map