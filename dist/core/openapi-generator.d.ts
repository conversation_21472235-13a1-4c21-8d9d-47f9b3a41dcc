import { <PERSON><PERSON>pen<PERSON><PERSON>enerator, IValidationAdapter } from './interfaces';
import { HttpMethod, RouteSchema, OpenAPISpec } from './types';
export declare class OpenAPIGenerator implements IOpenAPIGenerator {
    private spec;
    constructor(info?: {
        title?: string;
        version?: string;
        description?: string;
    });
    addRoute(path: string, method: HttpMethod, schema: RouteSchema, validationAdapter: IValidationAdapter, metadata?: {
        name?: string;
        description?: string;
        tags?: string[];
    }): void;
    getSpec(): OpenAPISpec;
    updateInfo(info: {
        title?: string;
        version?: string;
        description?: string;
    }): void;
    clear(): void;
    addSchemaComponent(name: string, schema: any): void;
    getSchemaReference(name: string): any;
}
//# sourceMappingURL=openapi-generator.d.ts.map