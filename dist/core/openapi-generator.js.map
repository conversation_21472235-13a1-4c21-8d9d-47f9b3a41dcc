{"version": 3, "file": "openapi-generator.js", "sourceRoot": "", "sources": ["../../src/core/openapi-generator.ts"], "names": [], "mappings": ";;;AAWA,MAAa,gBAAgB;IAG3B,YAAY,IAAiE;QAC3E,IAAI,CAAC,IAAI,GAAG;YACV,OAAO,EAAE,OAAO;YAChB,IAAI,EAAE;gBACJ,KAAK,EAAE,IAAI,EAAE,KAAK,IAAI,kBAAkB;gBACxC,OAAO,EAAE,IAAI,EAAE,OAAO,IAAI,OAAO;gBACjC,WAAW,EAAE,IAAI,EAAE,WAAW,IAAI,iCAAiC;aACpE;YACD,KAAK,EAAE,EAAE;YACT,UAAU,EAAE;gBACV,OAAO,EAAE,EAAE;gBACX,SAAS,EAAE,EAAE;gBACb,UAAU,EAAE,EAAE;aACf;SACF,CAAC;IACJ,CAAC;IAKD,QAAQ,CACN,IAAY,EACZ,MAAkB,EAClB,MAAmB,EACnB,iBAAqC,EACrC,QAIC;QAGD,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC;YAC3B,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;QAC7B,CAAC;QAGD,MAAM,SAAS,GAAQ;YACrB,OAAO,EAAE,QAAQ,EAAE,IAAI,IAAI,GAAG,MAAM,CAAC,WAAW,EAAE,IAAI,IAAI,EAAE;YAC5D,WAAW,EAAE,QAAQ,EAAE,WAAW,IAAI,GAAG,MAAM,CAAC,WAAW,EAAE,kBAAkB,IAAI,EAAE;YACrF,SAAS,EAAE;gBACT,KAAK,EAAE;oBACL,WAAW,EAAE,SAAS;iBACvB;gBACD,KAAK,EAAE;oBACL,WAAW,EAAE,aAAa;oBAC1B,OAAO,EAAE;wBACP,kBAAkB,EAAE;4BAClB,MAAM,EAAE;gCACN,IAAI,EAAE,QAAQ;gCACd,UAAU,EAAE;oCACV,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;oCACzB,OAAO,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;oCAC3B,OAAO,EAAE;wCACP,IAAI,EAAE,OAAO;wCACb,KAAK,EAAE;4CACL,IAAI,EAAE,QAAQ;4CACd,UAAU,EAAE;gDACV,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;gDACzB,OAAO,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;gDAC3B,KAAK,EAAE,EAAE;gDACT,IAAI,EAAE;oDACJ,IAAI,EAAE,OAAO;oDACb,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;iDAC1B;6CACF;yCACF;qCACF;iCACF;6BACF;yBACF;qBACF;iBACF;gBACD,KAAK,EAAE;oBACL,WAAW,EAAE,uBAAuB;oBACpC,OAAO,EAAE;wBACP,kBAAkB,EAAE;4BAClB,MAAM,EAAE;gCACN,IAAI,EAAE,QAAQ;gCACd,UAAU,EAAE;oCACV,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;oCACzB,OAAO,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;iCAC5B;6BACF;yBACF;qBACF;iBACF;aACF;SACF,CAAC;QAGF,IAAI,QAAQ,EAAE,IAAI,IAAI,QAAQ,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC/C,SAAS,CAAC,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC;QACjC,CAAC;QAGD,IAAI,MAAM,CAAC,IAAI,IAAI,iBAAiB,CAAC,aAAa,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;YAChE,SAAS,CAAC,WAAW,GAAG;gBACtB,QAAQ,EAAE,IAAI;gBACd,OAAO,EAAE;oBACP,kBAAkB,EAAE;wBAClB,MAAM,EAAE,iBAAiB,CAAC,eAAe,CAAC,MAAM,CAAC,IAAI,CAAC;qBACvD;iBACF;aACF,CAAC;QACJ,CAAC;QAGD,SAAS,CAAC,UAAU,GAAG,EAAE,CAAC;QAG1B,IAAI,MAAM,CAAC,KAAK,IAAI,iBAAiB,CAAC,aAAa,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC;YAClE,MAAM,WAAW,GAAG,iBAAiB,CAAC,eAAe,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAEpE,IAAI,WAAW,CAAC,IAAI,KAAK,QAAQ,IAAI,WAAW,CAAC,UAAU,EAAE,CAAC;gBAC5D,KAAK,MAAM,CAAC,SAAS,EAAE,WAAW,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,UAAU,CAAC,EAAE,CAAC;oBAC9E,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC;wBACxB,IAAI,EAAE,SAAS;wBACf,EAAE,EAAE,OAAO;wBACX,QAAQ,EAAE,WAAW,CAAC,QAAQ,EAAE,QAAQ,CAAC,SAAS,CAAC,IAAI,KAAK;wBAC5D,MAAM,EAAE,WAAW;qBACpB,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;QACH,CAAC;QAGD,IAAI,MAAM,CAAC,MAAM,IAAI,iBAAiB,CAAC,aAAa,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC;YACpE,MAAM,YAAY,GAAG,iBAAiB,CAAC,eAAe,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YAEtE,IAAI,YAAY,CAAC,IAAI,KAAK,QAAQ,IAAI,YAAY,CAAC,UAAU,EAAE,CAAC;gBAC9D,KAAK,MAAM,CAAC,SAAS,EAAE,WAAW,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,UAAU,CAAC,EAAE,CAAC;oBAC/E,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC;wBACxB,IAAI,EAAE,SAAS;wBACf,EAAE,EAAE,MAAM;wBACV,QAAQ,EAAE,IAAI;wBACd,MAAM,EAAE,WAAW;qBACpB,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;QACH,CAAC;QAGD,IAAI,MAAM,CAAC,QAAQ,IAAI,iBAAiB,CAAC,aAAa,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC;YACxE,SAAS,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG;gBAC3B,WAAW,EAAE,SAAS;gBACtB,OAAO,EAAE;oBACP,kBAAkB,EAAE;wBAClB,MAAM,EAAE,iBAAiB,CAAC,eAAe,CAAC,MAAM,CAAC,QAAQ,CAAC;qBAC3D;iBACF;aACF,CAAC;QACJ,CAAC;QAGD,IAAI,SAAS,CAAC,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACtC,OAAO,SAAS,CAAC,UAAU,CAAC;QAC9B,CAAC;QAGD,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,GAAG,SAAS,CAAC;IAC5C,CAAC;IAKD,OAAO;QACL,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IAC/C,CAAC;IAKD,UAAU,CAAC,IAAgE;QACzE,IAAI,IAAI,CAAC,KAAK;YAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QAClD,IAAI,IAAI,CAAC,OAAO;YAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;QACxD,IAAI,IAAI,CAAC,WAAW;YAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;IACtE,CAAC;IAKD,KAAK;QACH,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;QACrB,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;YACzB,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,GAAG,EAAE,CAAC;YAClC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,SAAS,GAAG,EAAE,CAAC;YACpC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,UAAU,GAAG,EAAE,CAAC;QACvC,CAAC;IACH,CAAC;IAKD,kBAAkB,CAAC,IAAY,EAAE,MAAW;QAC1C,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;YAC1B,IAAI,CAAC,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;QAC5B,CAAC;QACD,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;YAClC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,GAAG,EAAE,CAAC;QACpC,CAAC;QACD,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC;IAC9C,CAAC;IAKD,kBAAkB,CAAC,IAAY;QAC7B,OAAO,EAAE,IAAI,EAAE,wBAAwB,IAAI,EAAE,EAAE,CAAC;IAClD,CAAC;CACF;AArND,4CAqNC"}