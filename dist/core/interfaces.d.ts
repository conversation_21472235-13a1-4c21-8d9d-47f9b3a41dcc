import { HttpMethod, RequestData, ResponseData, RouteSchema, HandlerFunction, ValidationError, ErrorResponse, OpenAPISpec } from './types';
export interface IValidationAdapter<TSchema = any> {
    validate(data: any, schema: TSchema): Promise<any> | any;
    parseValidationError(error: any): ValidationError[];
    schemaToOpenAPI(schema: TSchema): any;
    isValidSchema(schema: any): schema is TSchema;
    getName(): string;
}
export interface IFrameworkAdapter {
    registerRoute(method: HttpMethod, path: string, handler: HandlerFunction, middleware?: any[]): void;
    createValidationMiddleware(schema: RouteSchema, validationAdapter: IValidationAdapter): any;
    createErrorHandler(customHandler?: (error: any) => ErrorResponse): any;
    extractRequestData(request: any): RequestData;
    sendResponse(response: any, data: ResponseData): void;
    getInstance(): any;
    getName(): string;
    listen(port: number, callback?: () => void): void;
}
export interface IOpenAPIGenerator {
    addRoute(path: string, method: HttpMethod, schema: RouteSchema, validationAdapter: IValidationAdapter, metadata?: {
        name?: string;
        description?: string;
        tags?: string[];
    }): void;
    getSpec(): OpenAPISpec;
    updateInfo(info: {
        title?: string;
        version?: string;
        description?: string;
    }): void;
    clear(): void;
}
export interface IExpressPlusLibrary {
    setFrameworkAdapter(adapter: IFrameworkAdapter): void;
    setValidationAdapter(adapter: IValidationAdapter): void;
    defineRoute(method: HttpMethod, path: string, handler: HandlerFunction, schema?: RouteSchema, options?: {
        middleware?: any[];
        metadata?: {
            name?: string;
            description?: string;
            tags?: string[];
        };
    }): void;
    getOpenAPISpec(): OpenAPISpec;
    getFrameworkInstance(): any;
    setupErrorHandling(customHandler?: (error: any) => ErrorResponse): void;
}
//# sourceMappingURL=interfaces.d.ts.map