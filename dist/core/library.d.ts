import { IExpressPlusLibrary, IFrameworkAdapter, IValidationAdapter } from './interfaces';
import { HttpMethod, RouteSchema, HandlerFunction, EnhancedHandler, OpenAPISpec, ErrorResponse, LibraryConfig, RequestData } from './types';
export declare function defineHandler<TSchema = any, TValidated = RequestData, TResponse = any>(options: {
    handler: HandlerFunction<TValidated, TResponse>;
    body?: TSchema;
    query?: TSchema;
    params?: TSchema;
    response?: TSchema;
    metadata?: {
        name?: string;
        description?: string;
        tags?: string[];
    };
}): EnhancedHandler<TSchema, TValidated, TResponse>;
export declare class ExpressPlusLibrary implements IExpressPlusLibrary {
    private frameworkAdapter?;
    private validationAdapter?;
    private openAPIGenerator;
    private config;
    constructor(config?: LibraryConfig);
    setFrameworkAdapter(adapter: IFrameworkAdapter): void;
    setValidationAdapter(adapter: IValidationAdapter): void;
    defineRoute(method: HttpMethod, path: string, handler: HandlerFunction, schema?: RouteSchema, options?: {
        middleware?: any[];
        metadata?: {
            name?: string;
            description?: string;
            tags?: string[];
        };
    }): void;
    getOpenAPISpec(): OpenAPISpec;
    getFrameworkInstance(): any;
    setupErrorHandling(customHandler?: (error: any) => ErrorResponse): void;
    private createDefaultErrorHandler;
    addOpenAPIEndpoint(path?: string): void;
    listen(port: number, callback?: () => void): void;
    getConfig(): LibraryConfig;
    updateConfig(config: Partial<LibraryConfig>): void;
}
//# sourceMappingURL=library.d.ts.map