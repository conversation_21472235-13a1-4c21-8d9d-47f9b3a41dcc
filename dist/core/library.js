"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ExpressPlusLibrary = void 0;
exports.defineHandler = defineHandler;
const openapi_generator_1 = require("./openapi-generator");
function defineHandler(options) {
    const { handler, metadata, ...schema } = options;
    const enhancedHandler = handler;
    enhancedHandler.__schema = schema;
    enhancedHandler.__metadata = metadata;
    return enhancedHandler;
}
class ExpressPlusLibrary {
    constructor(config) {
        this.config = {
            openapi: {
                title: 'Express Plus API',
                version: '1.0.0',
                description: 'API generated with Express Plus',
                ...config?.openapi
            },
            validation: {
                stripUnknown: true,
                abortEarly: false,
                ...config?.validation
            },
            errorHandling: {
                includeStack: process.env.NODE_ENV === 'development',
                ...config?.errorHandling
            }
        };
        this.openAPIGenerator = new openapi_generator_1.OpenAPIGenerator(this.config.openapi);
    }
    setFrameworkAdapter(adapter) {
        this.frameworkAdapter = adapter;
    }
    setValidationAdapter(adapter) {
        this.validationAdapter = adapter;
    }
    defineRoute(method, path, handler, schema, options) {
        if (!this.frameworkAdapter) {
            throw new Error('Framework adapter not set. Call setFrameworkAdapter() first.');
        }
        if (!this.validationAdapter) {
            throw new Error('Validation adapter not set. Call setValidationAdapter() first.');
        }
        const finalSchema = schema || handler.__schema || {};
        const middleware = options?.middleware || [];
        if (Object.keys(finalSchema).length > 0) {
            const validationMiddleware = this.frameworkAdapter.createValidationMiddleware(finalSchema, this.validationAdapter);
            middleware.unshift(validationMiddleware);
        }
        this.frameworkAdapter.registerRoute(method, path, handler, middleware);
        this.openAPIGenerator.addRoute(path, method, finalSchema, this.validationAdapter, options?.metadata);
    }
    getOpenAPISpec() {
        return this.openAPIGenerator.getSpec();
    }
    getFrameworkInstance() {
        if (!this.frameworkAdapter) {
            throw new Error('Framework adapter not set. Call setFrameworkAdapter() first.');
        }
        return this.frameworkAdapter.getInstance();
    }
    setupErrorHandling(customHandler) {
        if (!this.frameworkAdapter) {
            throw new Error('Framework adapter not set. Call setFrameworkAdapter() first.');
        }
        const errorHandler = this.frameworkAdapter.createErrorHandler(customHandler || this.createDefaultErrorHandler());
        const app = this.frameworkAdapter.getInstance();
        if (app && typeof app.use === 'function') {
            app.use(errorHandler);
        }
    }
    createDefaultErrorHandler() {
        return (error) => {
            const includeStack = this.config.errorHandling?.includeStack || false;
            const errorResponse = {
                error: 'Internal Server Error',
                message: includeStack ? error.message : 'Something went wrong',
                statusCode: 500
            };
            if (includeStack && error.stack) {
                errorResponse.stack = error.stack;
            }
            return errorResponse;
        };
    }
    addOpenAPIEndpoint(path = '/docs/openapi.json') {
        if (!this.frameworkAdapter) {
            throw new Error('Framework adapter not set. Call setFrameworkAdapter() first.');
        }
        const handler = () => {
            return this.getOpenAPISpec();
        };
        this.frameworkAdapter.registerRoute('get', path, handler);
    }
    listen(port, callback) {
        if (!this.frameworkAdapter) {
            throw new Error('Framework adapter not set. Call setFrameworkAdapter() first.');
        }
        this.frameworkAdapter.listen(port, callback);
    }
    getConfig() {
        return { ...this.config };
    }
    updateConfig(config) {
        this.config = {
            ...this.config,
            ...config,
            openapi: { ...this.config.openapi, ...config.openapi },
            validation: { ...this.config.validation, ...config.validation },
            errorHandling: { ...this.config.errorHandling, ...config.errorHandling }
        };
        if (config.openapi) {
            this.openAPIGenerator.updateInfo(config.openapi);
        }
    }
}
exports.ExpressPlusLibrary = ExpressPlusLibrary;
//# sourceMappingURL=library.js.map