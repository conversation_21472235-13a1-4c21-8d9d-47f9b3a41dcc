{"version": 3, "file": "openapi-generator.d.ts", "sourceRoot": "", "sources": ["../../src/core/openapi-generator.ts"], "names": [], "mappings": "AAKA,OAAO,EAAE,iBAAiB,EAAE,kBAAkB,EAAE,MAAM,cAAc,CAAC;AACrE,OAAO,EAAE,UAAU,EAAE,WAAW,EAAE,WAAW,EAAE,MAAM,SAAS,CAAC;AAK/D,qBAAa,gBAAiB,YAAW,iBAAiB;IACxD,OAAO,CAAC,IAAI,CAAc;gBAEd,IAAI,CAAC,EAAE;QAAE,KAAK,CAAC,EAAE,MAAM,CAAC;QAAC,OAAO,CAAC,EAAE,MAAM,CAAC;QAAC,WAAW,CAAC,EAAE,MAAM,CAAA;KAAE;IAoB7E,QAAQ,CACN,IAAI,EAAE,MAAM,EACZ,MAAM,EAAE,UAAU,EAClB,MAAM,EAAE,WAAW,EACnB,iBAAiB,EAAE,kBAAkB,EACrC,QAAQ,CAAC,EAAE;QACT,IAAI,CAAC,EAAE,MAAM,CAAC;QACd,WAAW,CAAC,EAAE,MAAM,CAAC;QACrB,IAAI,CAAC,EAAE,MAAM,EAAE,CAAC;KACjB,GACA,IAAI;IAwIP,OAAO,IAAI,WAAW;IAOtB,UAAU,CAAC,IAAI,EAAE;QAAE,KAAK,CAAC,EAAE,MAAM,CAAC;QAAC,OAAO,CAAC,EAAE,MAAM,CAAC;QAAC,WAAW,CAAC,EAAE,MAAM,CAAA;KAAE,GAAG,IAAI;IASlF,KAAK,IAAI,IAAI;IAYb,kBAAkB,CAAC,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,GAAG,IAAI;IAanD,kBAAkB,CAAC,IAAI,EAAE,MAAM,GAAG,GAAG;CAGtC"}