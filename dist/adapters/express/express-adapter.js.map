{"version": 3, "file": "express-adapter.js", "sourceRoot": "", "sources": ["../../../src/adapters/express/express-adapter.ts"], "names": [], "mappings": ";;;;;;AAKA,sDAAgG;AAkBhG,MAAa,sBAAuB,SAAQ,KAAK;IAC/C,YACE,OAAe,EACR,KAAa,EACb,gBAAmC;QAE1C,KAAK,CAAC,OAAO,CAAC,CAAC;QAHR,UAAK,GAAL,KAAK,CAAQ;QACb,qBAAgB,GAAhB,gBAAgB,CAAmB;QAG1C,IAAI,CAAC,IAAI,GAAG,wBAAwB,CAAC;IACvC,CAAC;CACF;AATD,wDASC;AAKD,MAAa,cAAc;IAGzB,YAAY,GAAiB;QAC3B,IAAI,CAAC,GAAG,GAAG,GAAG,IAAI,IAAA,iBAAO,GAAE,CAAC;QAG5B,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,iBAAO,CAAC,IAAI,EAAE,CAAC,CAAC;QAC7B,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,iBAAO,CAAC,UAAU,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;IACvD,CAAC;IAKD,aAAa,CACX,MAAkB,EAClB,IAAY,EACZ,OAAwB,EACxB,aAA+B,EAAE;QAEjC,MAAM,cAAc,GAAmB,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;YAC/F,IAAI,CAAC;gBAEH,MAAM,SAAS,GAAI,GAAW,CAAC,SAAS,IAAI,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC;gBAGzE,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,SAAS,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;gBAGtD,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC;oBACrB,IAAI,CAAC,YAAY,CAAC,GAAG,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC,CAAC;gBAC3C,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAI,CAAC,KAAK,CAAC,CAAC;YACd,CAAC;QACH,CAAC,CAAC;QAGF,MAAM,aAAa,GAAG,CAAC,GAAG,UAAU,EAAE,cAAc,CAAC,CAAC;QACtD,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,GAAG,aAAa,CAAC,CAAC;IAC3C,CAAC;IAKD,0BAA0B,CACxB,MAAmB,EACnB,iBAAqC;QAErC,OAAO,KAAK,EAAE,GAAY,EAAE,IAAc,EAAE,IAAkB,EAAE,EAAE;YAChE,IAAI,CAAC;gBACH,MAAM,SAAS,GAAQ,EAAE,CAAC;gBAG1B,IAAI,MAAM,CAAC,IAAI,EAAE,CAAC;oBAChB,IAAI,CAAC;wBACH,SAAS,CAAC,IAAI,GAAG,MAAM,iBAAiB,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC;oBAC3E,CAAC;oBAAC,OAAO,KAAK,EAAE,CAAC;wBACf,MAAM,gBAAgB,GAAG,iBAAiB,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;wBACvE,MAAM,IAAI,sBAAsB,CAAC,wBAAwB,EAAE,MAAM,EAAE,gBAAgB,CAAC,CAAC;oBACvF,CAAC;gBACH,CAAC;gBAGD,IAAI,MAAM,CAAC,KAAK,EAAE,CAAC;oBACjB,IAAI,CAAC;wBACH,SAAS,CAAC,KAAK,GAAG,MAAM,iBAAiB,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC;oBAC9E,CAAC;oBAAC,OAAO,KAAK,EAAE,CAAC;wBACf,MAAM,gBAAgB,GAAG,iBAAiB,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;wBACvE,MAAM,IAAI,sBAAsB,CAAC,yBAAyB,EAAE,OAAO,EAAE,gBAAgB,CAAC,CAAC;oBACzF,CAAC;gBACH,CAAC;gBAGD,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;oBAClB,IAAI,CAAC;wBACH,SAAS,CAAC,MAAM,GAAG,MAAM,iBAAiB,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;oBACjF,CAAC;oBAAC,OAAO,KAAK,EAAE,CAAC;wBACf,MAAM,gBAAgB,GAAG,iBAAiB,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;wBACvE,MAAM,IAAI,sBAAsB,CAAC,0BAA0B,EAAE,QAAQ,EAAE,gBAAgB,CAAC,CAAC;oBAC3F,CAAC;gBACH,CAAC;gBAGA,GAAW,CAAC,SAAS,GAAG,SAAS,CAAC;gBACnC,IAAI,EAAE,CAAC;YACT,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAI,CAAC,KAAK,CAAC,CAAC;YACd,CAAC;QACH,CAAC,CAAC;IACJ,CAAC;IAKD,kBAAkB,CAAC,aAA6C;QAC9D,OAAO,CAAC,KAAU,EAAE,IAAa,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;YAEtE,IAAI,GAAG,CAAC,WAAW,EAAE,CAAC;gBACpB,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC;YACrB,CAAC;YAED,IAAI,aAA4B,CAAC;YAEjC,IAAI,aAAa,EAAE,CAAC;gBAClB,aAAa,GAAG,aAAa,CAAC,KAAK,CAAC,CAAC;YACvC,CAAC;iBAAM,CAAC;gBAEN,IAAI,KAAK,YAAY,sBAAsB,EAAE,CAAC;oBAC5C,aAAa,GAAG;wBACd,KAAK,EAAE,kBAAkB;wBACzB,OAAO,EAAE,KAAK,CAAC,OAAO;wBACtB,OAAO,EAAE,KAAK,CAAC,gBAAgB;wBAC/B,UAAU,EAAE,GAAG;qBAChB,CAAC;gBACJ,CAAC;qBAAM,CAAC;oBACN,aAAa,GAAG;wBACd,KAAK,EAAE,uBAAuB;wBAC9B,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,sBAAsB;wBACxF,UAAU,EAAE,GAAG;qBAChB,CAAC;gBACJ,CAAC;YACH,CAAC;YAED,GAAG,CAAC,MAAM,CAAC,aAAa,CAAC,UAAU,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAClE,CAAC,CAAC;IACJ,CAAC;IAKD,kBAAkB,CAAC,OAAgB;QACjC,OAAO;YACL,IAAI,EAAE,OAAO,CAAC,IAAI;YAClB,KAAK,EAAE,OAAO,CAAC,KAAK;YACpB,MAAM,EAAE,OAAO,CAAC,MAAM;YACtB,OAAO,EAAE,OAAO,CAAC,OAAiC;SACnD,CAAC;IACJ,CAAC;IAKD,YAAY,CAAC,QAAkB,EAAE,IAAkB;QACjD,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACjB,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE;gBACpD,QAAQ,CAAC,SAAS,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;YACjC,CAAC,CAAC,CAAC;QACL,CAAC;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,IAAI,GAAG,CAAC;QAElC,IAAI,IAAI,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;YAC5B,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC1C,CAAC;aAAM,CAAC;YACN,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC;QAChC,CAAC;IACH,CAAC;IAKD,WAAW;QACT,OAAO,IAAI,CAAC,GAAG,CAAC;IAClB,CAAC;IAKD,OAAO;QACL,OAAO,SAAS,CAAC;IACnB,CAAC;IAKD,MAAM,CAAC,IAAY,EAAE,QAAqB;QACxC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;IAClC,CAAC;CACF;AAnLD,wCAmLC"}