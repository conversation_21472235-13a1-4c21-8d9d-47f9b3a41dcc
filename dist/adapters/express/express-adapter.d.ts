import { Application, Request, Response, RequestHandler } from 'express';
import { IFrameworkAdapter, IValidationAdapter } from '../../core/interfaces';
import { HttpMethod, RequestData, ResponseData, RouteSchema, HandlerFunction, ErrorResponse, ValidationError } from '../../core/types';
export declare class ExpressValidationError extends Error {
    field: string;
    validationErrors: ValidationError[];
    constructor(message: string, field: string, validationErrors: ValidationError[]);
}
export declare class ExpressAdapter implements IFrameworkAdapter {
    private app;
    constructor(app?: Application);
    registerRoute(method: HttpMethod, path: string, handler: HandlerFunction, middleware?: RequestHandler[]): void;
    createValidationMiddleware(schema: RouteSchema, validationAdapter: IValidationAdapter): RequestHandler;
    createErrorHandler(customHandler?: (error: any) => ErrorResponse): any;
    extractRequestData(request: Request): RequestData;
    sendResponse(response: Response, data: ResponseData): void;
    getInstance(): Application;
    getName(): string;
    listen(port: number, callback?: () => void): void;
}
//# sourceMappingURL=express-adapter.d.ts.map