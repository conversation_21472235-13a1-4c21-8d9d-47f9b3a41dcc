"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ExpressAdapter = exports.ExpressValidationError = void 0;
const express_1 = __importDefault(require("express"));
class ExpressValidationError extends Error {
    constructor(message, field, validationErrors) {
        super(message);
        this.field = field;
        this.validationErrors = validationErrors;
        this.name = 'ExpressValidationError';
    }
}
exports.ExpressValidationError = ExpressValidationError;
class ExpressAdapter {
    constructor(app) {
        this.app = app || (0, express_1.default)();
        this.app.use(express_1.default.json());
        this.app.use(express_1.default.urlencoded({ extended: true }));
    }
    registerRoute(method, path, handler, middleware = []) {
        const expressHandler = async (req, res, next) => {
            try {
                const validated = req.validated || this.extractRequestData(req);
                const result = await handler(validated, { req, res });
                if (!res.headersSent) {
                    this.sendResponse(res, { body: result });
                }
            }
            catch (error) {
                next(error);
            }
        };
        const allMiddleware = [...middleware, expressHandler];
        this.app[method](path, ...allMiddleware);
    }
    createValidationMiddleware(schema, validationAdapter) {
        return async (req, _res, next) => {
            try {
                const validated = {};
                if (schema.body) {
                    try {
                        validated.body = await validationAdapter.validate(req.body, schema.body);
                    }
                    catch (error) {
                        const validationErrors = validationAdapter.parseValidationError(error);
                        throw new ExpressValidationError('Body validation failed', 'body', validationErrors);
                    }
                }
                if (schema.query) {
                    try {
                        validated.query = await validationAdapter.validate(req.query, schema.query);
                    }
                    catch (error) {
                        const validationErrors = validationAdapter.parseValidationError(error);
                        throw new ExpressValidationError('Query validation failed', 'query', validationErrors);
                    }
                }
                if (schema.params) {
                    try {
                        validated.params = await validationAdapter.validate(req.params, schema.params);
                    }
                    catch (error) {
                        const validationErrors = validationAdapter.parseValidationError(error);
                        throw new ExpressValidationError('Params validation failed', 'params', validationErrors);
                    }
                }
                req.validated = validated;
                next();
            }
            catch (error) {
                next(error);
            }
        };
    }
    createErrorHandler(customHandler) {
        return (error, _req, res, next) => {
            if (res.headersSent) {
                return next(error);
            }
            let errorResponse;
            if (customHandler) {
                errorResponse = customHandler(error);
            }
            else {
                if (error instanceof ExpressValidationError) {
                    errorResponse = {
                        error: 'Validation Error',
                        message: error.message,
                        details: error.validationErrors,
                        statusCode: 400
                    };
                }
                else {
                    errorResponse = {
                        error: 'Internal Server Error',
                        message: process.env.NODE_ENV === 'development' ? error.message : 'Something went wrong',
                        statusCode: 500
                    };
                }
            }
            res.status(errorResponse.statusCode || 500).json(errorResponse);
        };
    }
    extractRequestData(request) {
        return {
            body: request.body,
            query: request.query,
            params: request.params,
            headers: request.headers
        };
    }
    sendResponse(response, data) {
        if (data.headers) {
            Object.entries(data.headers).forEach(([key, value]) => {
                response.setHeader(key, value);
            });
        }
        const status = data.status || 200;
        if (data.body !== undefined) {
            response.status(status).json(data.body);
        }
        else {
            response.status(status).end();
        }
    }
    getInstance() {
        return this.app;
    }
    getName() {
        return 'express';
    }
    listen(port, callback) {
        this.app.listen(port, callback);
    }
}
exports.ExpressAdapter = ExpressAdapter;
//# sourceMappingURL=express-adapter.js.map