import { ZodSchema } from 'zod';
import { IValidationAdapter } from '../../core/interfaces';
import { ValidationError } from '../../core/types';
export declare class ZodAdapter implements IValidationAdapter<ZodSchema> {
    validate(data: any, schema: ZodSchema): any;
    parseValidationError(error: any): ValidationError[];
    schemaToOpenAPI(schema: ZodSchema): any;
    isValidSchema(schema: any): schema is ZodSchema;
    getName(): string;
    private zodToOpenAPI;
}
//# sourceMappingURL=zod-adapter.d.ts.map