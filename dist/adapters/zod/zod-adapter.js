"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ZodAdapter = void 0;
const zod_1 = require("zod");
class ZodAdapter {
    validate(data, schema) {
        return schema.parse(data);
    }
    parseValidationError(error) {
        if (!(error instanceof zod_1.ZodError)) {
            return [{
                    field: 'unknown',
                    message: error.message || 'Unknown validation error',
                    value: undefined,
                    path: []
                }];
        }
        return error.errors.map(zodError => ({
            field: zodError.path.join('.') || 'root',
            message: zodError.message,
            value: zodError.received,
            path: zodError.path.map(p => String(p))
        }));
    }
    schemaToOpenAPI(schema) {
        return this.zodToOpenAPI(schema);
    }
    isValidSchema(schema) {
        return schema instanceof zod_1.ZodType;
    }
    getName() {
        return 'zod';
    }
    zodToOpenAPI(schema) {
        if (schema instanceof zod_1.z.ZodOptional) {
            const innerSchema = this.zodToOpenAPI(schema._def.innerType);
            return { ...innerSchema, nullable: false };
        }
        if (schema instanceof zod_1.z.ZodNullable) {
            const innerSchema = this.zodToOpenAPI(schema._def.innerType);
            return { ...innerSchema, nullable: true };
        }
        if (schema instanceof zod_1.z.ZodDefault) {
            const innerSchema = this.zodToOpenAPI(schema._def.innerType);
            return { ...innerSchema, default: schema._def.defaultValue() };
        }
        if (schema instanceof zod_1.z.ZodString) {
            const result = { type: 'string' };
            if (schema._def.checks) {
                for (const check of schema._def.checks) {
                    switch (check.kind) {
                        case 'min':
                            result.minLength = check.value;
                            break;
                        case 'max':
                            result.maxLength = check.value;
                            break;
                        case 'email':
                            result.format = 'email';
                            break;
                        case 'url':
                            result.format = 'uri';
                            break;
                        case 'uuid':
                            result.format = 'uuid';
                            break;
                        case 'regex':
                            result.pattern = check.regex.source;
                            break;
                    }
                }
            }
            return result;
        }
        if (schema instanceof zod_1.z.ZodNumber) {
            const result = { type: 'number' };
            if (schema._def.checks) {
                for (const check of schema._def.checks) {
                    switch (check.kind) {
                        case 'min':
                            result.minimum = check.value;
                            if (!check.inclusive)
                                result.exclusiveMinimum = true;
                            break;
                        case 'max':
                            result.maximum = check.value;
                            if (!check.inclusive)
                                result.exclusiveMaximum = true;
                            break;
                        case 'int':
                            result.type = 'integer';
                            break;
                    }
                }
            }
            return result;
        }
        if (schema instanceof zod_1.z.ZodBoolean) {
            return { type: 'boolean' };
        }
        if (schema instanceof zod_1.z.ZodDate) {
            return { type: 'string', format: 'date-time' };
        }
        if (schema instanceof zod_1.z.ZodObject) {
            const properties = {};
            const required = [];
            const shape = schema._def.shape();
            for (const [key, value] of Object.entries(shape)) {
                const propertySchema = this.zodToOpenAPI(value);
                properties[key] = propertySchema;
                if (!(value instanceof zod_1.z.ZodOptional)) {
                    required.push(key);
                }
            }
            const result = {
                type: 'object',
                properties
            };
            if (required.length > 0) {
                result.required = required;
            }
            return result;
        }
        if (schema instanceof zod_1.z.ZodArray) {
            return {
                type: 'array',
                items: this.zodToOpenAPI(schema._def.type)
            };
        }
        if (schema instanceof zod_1.z.ZodEnum) {
            return {
                type: 'string',
                enum: schema._def.values
            };
        }
        if (schema instanceof zod_1.z.ZodLiteral) {
            const value = schema._def.value;
            return {
                type: typeof value,
                enum: [value]
            };
        }
        if (schema instanceof zod_1.z.ZodUnion) {
            return {
                oneOf: schema._def.options.map((option) => this.zodToOpenAPI(option))
            };
        }
        if (schema instanceof zod_1.z.ZodIntersection) {
            return {
                allOf: [
                    this.zodToOpenAPI(schema._def.left),
                    this.zodToOpenAPI(schema._def.right)
                ]
            };
        }
        if (schema instanceof zod_1.z.ZodRecord) {
            return {
                type: 'object',
                additionalProperties: this.zodToOpenAPI(schema._def.valueType)
            };
        }
        if (schema instanceof zod_1.z.ZodTuple) {
            return {
                type: 'array',
                items: schema._def.items.map((item) => this.zodToOpenAPI(item)),
                minItems: schema._def.items.length,
                maxItems: schema._def.items.length
            };
        }
        return { type: 'object' };
    }
}
exports.ZodAdapter = ZodAdapter;
//# sourceMappingURL=zod-adapter.js.map