/**
 * Basic Usage Example
 * Demonstrates how to use Express Plus with Express and Zod adapters
 */

import { z } from 'zod';
import { 
  ExpressPlusLibrary, 
  ExpressAdapter, 
  ZodAdapter, 
  defineHandler 
} from '../src';

// Initialize the library with Express and Zod adapters
const library = new ExpressPlusLibrary({
  openapi: {
    title: 'User Management API',
    version: '1.0.0',
    description: 'A simple user management API built with Express Plus'
  }
});

// Set up adapters
const expressAdapter = new ExpressAdapter();
const zodAdapter = new ZodAdapter();

library.setFrameworkAdapter(expressAdapter);
library.setValidationAdapter(zodAdapter);

// Define schemas
const CreateUserSchema = {
  body: z.object({
    name: z.string().min(1, 'Name is required'),
    email: z.string().email('Invalid email format'),
    age: z.number().min(18, 'Must be at least 18 years old').max(120, 'Invalid age')
  }),
  response: z.object({
    id: z.string(),
    name: z.string(),
    email: z.string(),
    age: z.number(),
    createdAt: z.string()
  })
};

const GetUserSchema = {
  params: z.object({
    id: z.string().uuid('Invalid user ID format')
  }),
  response: z.object({
    id: z.string(),
    name: z.string(),
    email: z.string(),
    age: z.number(),
    createdAt: z.string()
  })
};

const UpdateUserSchema = {
  params: z.object({
    id: z.string().uuid('Invalid user ID format')
  }),
  body: z.object({
    name: z.string().min(1).optional(),
    email: z.string().email().optional(),
    age: z.number().min(18).max(120).optional()
  }).refine(data => Object.keys(data).length > 0, {
    message: 'At least one field must be provided'
  }),
  response: z.object({
    id: z.string(),
    name: z.string(),
    email: z.string(),
    age: z.number(),
    createdAt: z.string(),
    updatedAt: z.string()
  })
};

const ListUsersSchema = {
  query: z.object({
    page: z.string().transform(val => parseInt(val, 10)).pipe(z.number().min(1)).optional(),
    limit: z.string().transform(val => parseInt(val, 10)).pipe(z.number().min(1).max(100)).optional(),
    search: z.string().optional()
  }),
  response: z.object({
    users: z.array(z.object({
      id: z.string(),
      name: z.string(),
      email: z.string(),
      age: z.number(),
      createdAt: z.string()
    })),
    pagination: z.object({
      page: z.number(),
      limit: z.number(),
      total: z.number(),
      totalPages: z.number()
    })
  })
};

// Mock database
interface User {
  id: string;
  name: string;
  email: string;
  age: number;
  createdAt: string;
  updatedAt?: string;
}

const users: User[] = [
  {
    id: '123e4567-e89b-12d3-a456-426614174000',
    name: 'John Doe',
    email: '<EMAIL>',
    age: 30,
    createdAt: '2024-01-01T00:00:00Z'
  },
  {
    id: '123e4567-e89b-12d3-a456-426614174001',
    name: 'Jane Smith',
    email: '<EMAIL>',
    age: 25,
    createdAt: '2024-01-02T00:00:00Z'
  }
];

// Define handlers using the defineHandler utility
const createUser = defineHandler({
  ...CreateUserSchema,
  metadata: {
    name: 'Create User',
    description: 'Create a new user in the system',
    tags: ['users']
  },
  handler: async ({ body }) => {
    const newUser: User = {
      id: `123e4567-e89b-12d3-a456-${Date.now()}`,
      ...body,
      createdAt: new Date().toISOString()
    };
    
    users.push(newUser);
    return newUser;
  }
});

const getUser = defineHandler({
  ...GetUserSchema,
  metadata: {
    name: 'Get User',
    description: 'Retrieve a user by ID',
    tags: ['users']
  },
  handler: async ({ params }) => {
    const user = users.find(u => u.id === params.id);
    if (!user) {
      throw new Error('User not found');
    }
    return user;
  }
});

const updateUser = defineHandler({
  ...UpdateUserSchema,
  metadata: {
    name: 'Update User',
    description: 'Update an existing user',
    tags: ['users']
  },
  handler: async ({ params, body }) => {
    const userIndex = users.findIndex(u => u.id === params.id);
    if (userIndex === -1) {
      throw new Error('User not found');
    }
    
    const updatedUser = {
      ...users[userIndex],
      ...body,
      updatedAt: new Date().toISOString()
    };
    
    users[userIndex] = updatedUser;
    return updatedUser;
  }
});

const listUsers = defineHandler({
  ...ListUsersSchema,
  metadata: {
    name: 'List Users',
    description: 'Get a paginated list of users',
    tags: ['users']
  },
  handler: async ({ query }) => {
    const page = query?.page || 1;
    const limit = query?.limit || 10;
    const search = query?.search;
    
    let filteredUsers = users;
    
    if (search) {
      filteredUsers = users.filter(user => 
        user.name.toLowerCase().includes(search.toLowerCase()) ||
        user.email.toLowerCase().includes(search.toLowerCase())
      );
    }
    
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedUsers = filteredUsers.slice(startIndex, endIndex);
    
    return {
      users: paginatedUsers,
      pagination: {
        page,
        limit,
        total: filteredUsers.length,
        totalPages: Math.ceil(filteredUsers.length / limit)
      }
    };
  }
});

// Register routes
library.defineRoute('post', '/users', createUser);
library.defineRoute('get', '/users/:id', getUser);
library.defineRoute('put', '/users/:id', updateUser);
library.defineRoute('get', '/users', listUsers);

// Add health check endpoint (without validation)
library.defineRoute('get', '/health', async () => ({
  status: 'ok',
  timestamp: new Date().toISOString(),
  uptime: process.uptime()
}), undefined, {
  metadata: {
    name: 'Health Check',
    description: 'Check if the API is running',
    tags: ['system']
  }
});

// Setup error handling and OpenAPI endpoint
library.setupErrorHandling();
library.addOpenAPIEndpoint('/docs/openapi.json');

// Start the server
const PORT = process.env.PORT || 3000;
library.listen(PORT, () => {
  console.log(`🚀 Server running on port ${PORT}`);
  console.log(`📚 OpenAPI docs available at http://localhost:${PORT}/docs/openapi.json`);
  console.log('\n📋 Available endpoints:');
  console.log('  POST   /users           - Create a new user');
  console.log('  GET    /users           - List users (with pagination)');
  console.log('  GET    /users/:id       - Get user by ID');
  console.log('  PUT    /users/:id       - Update user');
  console.log('  GET    /health          - Health check');
  console.log('  GET    /docs/openapi.json - OpenAPI specification');
});

export default library;
