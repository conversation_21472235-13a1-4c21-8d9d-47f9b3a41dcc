/**
 * Adapter Pattern Demonstration
 * Shows how to swap between different framework and validation adapters
 */

import { z } from 'zod';
import * as Jo<PERSON> from 'joi';
import { 
  ExpressPlusLibrary, 
  ExpressAdapter, 
  ZodAdapter,
  define<PERSON><PERSON><PERSON>,
  IValidationAdapter,
  ValidationError
} from '../src';

// Create a simple Joi adapter to demonstrate the adapter pattern
class JoiAdapter implements IValidationAdapter<Joi.Schema> {
  validate(data: any, schema: Joi.Schema): any {
    const result = schema.validate(data);
    if (result.error) {
      throw result.error;
    }
    return result.value;
  }

  parseValidationError(error: any): ValidationError[] {
    if (error.isJoi) {
      return error.details.map((detail: any) => ({
        field: detail.path.join('.') || 'root',
        message: detail.message,
        value: detail.context?.value,
        path: detail.path
      }));
    }
    return [{
      field: 'unknown',
      message: error.message || 'Unknown validation error',
      value: undefined,
      path: []
    }];
  }

  schemaToOpenAPI(schema: Joi.Schema): any {
    // Simplified <PERSON><PERSON> to OpenAPI conversion
    const description = schema.describe();
    return this.joiDescriptionToOpenAPI(description);
  }

  isValidSchema(schema: any): schema is Joi.Schema {
    return schema && typeof schema.validate === 'function' && schema.isJoi === true;
  }

  getName(): string {
    return 'joi';
  }

  private joiDescriptionToOpenAPI(description: any): any {
    switch (description.type) {
      case 'string':
        const stringSchema: any = { type: 'string' };
        if (description.rules) {
          for (const rule of description.rules) {
            if (rule.name === 'min') stringSchema.minLength = rule.args.limit;
            if (rule.name === 'max') stringSchema.maxLength = rule.args.limit;
            if (rule.name === 'email') stringSchema.format = 'email';
          }
        }
        return stringSchema;
      case 'number':
        const numberSchema: any = { type: 'number' };
        if (description.rules) {
          for (const rule of description.rules) {
            if (rule.name === 'min') numberSchema.minimum = rule.args.limit;
            if (rule.name === 'max') numberSchema.maximum = rule.args.limit;
            if (rule.name === 'integer') numberSchema.type = 'integer';
          }
        }
        return numberSchema;
      case 'boolean':
        return { type: 'boolean' };
      case 'object':
        const objectSchema: any = { type: 'object', properties: {} };
        if (description.keys) {
          for (const [key, value] of Object.entries(description.keys)) {
            objectSchema.properties[key] = this.joiDescriptionToOpenAPI(value);
          }
        }
        return objectSchema;
      case 'array':
        return {
          type: 'array',
          items: description.items ? this.joiDescriptionToOpenAPI(description.items[0]) : {}
        };
      default:
        return { type: 'object' };
    }
  }
}

// Demo function to show adapter swapping
async function demonstrateAdapterPattern() {
  console.log('🔄 Demonstrating Adapter Pattern\n');

  // Create library instance
  const library = new ExpressPlusLibrary({
    openapi: {
      title: 'Adapter Pattern Demo API',
      version: '1.0.0',
      description: 'Demonstrates swapping between validation adapters'
    }
  });

  // Set framework adapter (Express)
  const expressAdapter = new ExpressAdapter();
  library.setFrameworkAdapter(expressAdapter);

  // Demo 1: Using Zod adapter
  console.log('📦 Demo 1: Using Zod Adapter');
  const zodAdapter = new ZodAdapter();
  library.setValidationAdapter(zodAdapter);

  const zodUserSchema = {
    body: z.object({
      name: z.string().min(1),
      email: z.string().email(),
      age: z.number().min(18)
    })
  };

  const zodHandler = defineHandler({
    ...zodUserSchema,
    metadata: {
      name: 'Create User (Zod)',
      description: 'Create user with Zod validation'
    },
    handler: async ({ body }) => {
      console.log(`✅ Zod validation passed for: ${body.name}`);
      return { id: '1', ...body, adapter: 'zod' };
    }
  });

  library.defineRoute('post', '/users/zod', zodHandler);

  // Demo 2: Switching to Joi adapter
  console.log('📦 Demo 2: Switching to Joi Adapter');
  const joiAdapter = new JoiAdapter();
  library.setValidationAdapter(joiAdapter);

  const joiUserSchema = {
    body: Joi.object({
      name: Joi.string().min(1).required(),
      email: Joi.string().email().required(),
      age: Joi.number().min(18).required()
    })
  };

  const joiHandler = defineHandler({
    ...joiUserSchema,
    metadata: {
      name: 'Create User (Joi)',
      description: 'Create user with Joi validation'
    },
    handler: async ({ body }) => {
      console.log(`✅ Joi validation passed for: ${body.name}`);
      return { id: '2', ...body, adapter: 'joi' };
    }
  });

  library.defineRoute('post', '/users/joi', joiHandler);

  // Add a route that shows current adapter info
  library.defineRoute('get', '/adapter-info', async () => {
    return {
      framework: expressAdapter.getName(),
      validation: library.getConfig(),
      message: 'This endpoint shows which adapters are currently active'
    };
  });

  // Setup error handling and OpenAPI
  library.setupErrorHandling();
  library.addOpenAPIEndpoint('/docs/openapi.json');

  console.log('\n🚀 Starting server with adapter pattern demo...');
  
  const PORT = 3001;
  library.listen(PORT, () => {
    console.log(`\n🌐 Server running on http://localhost:${PORT}`);
    console.log('\n📋 Try these endpoints:');
    console.log(`  POST http://localhost:${PORT}/users/zod`);
    console.log('       Body: {"name": "John", "email": "<EMAIL>", "age": 25}');
    console.log(`  POST http://localhost:${PORT}/users/joi`);
    console.log('       Body: {"name": "Jane", "email": "<EMAIL>", "age": 30}');
    console.log(`  GET  http://localhost:${PORT}/adapter-info`);
    console.log(`  GET  http://localhost:${PORT}/docs/openapi.json`);
    
    console.log('\n💡 Key Points:');
    console.log('  • Same ExpressPlusLibrary instance');
    console.log('  • Different validation adapters (Zod vs Joi)');
    console.log('  • Consistent API regardless of underlying libraries');
    console.log('  • OpenAPI generation works with both adapters');
  });

  return library;
}

// Export for use in other examples
export { JoiAdapter, demonstrateAdapterPattern };

// Run the demo if this file is executed directly
if (require.main === module) {
  demonstrateAdapterPattern().catch(console.error);
}
