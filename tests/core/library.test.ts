/**
 * Tests for the main ExpressPlusLibrary class
 */

import { z } from 'zod';
import {
  ExpressPlusLibrary,
  ExpressAdapter,
  ZodAdapter,
  defineHandler
} from '../../src';

describe('ExpressPlusLibrary', () => {
  let library: ExpressPlusLibrary;
  let expressAdapter: ExpressAdapter;
  let zodAdapter: ZodAdapter;

  beforeEach(() => {
    library = new ExpressPlusLibrary({
      openapi: {
        title: 'Test API',
        version: '1.0.0'
      }
    });
    expressAdapter = new ExpressAdapter();
    zodAdapter = new ZodAdapter();
  });

  describe('Adapter Management', () => {
    test('should set framework adapter', () => {
      expect(() => library.setFrameworkAdapter(expressAdapter)).not.toThrow();
    });

    test('should set validation adapter', () => {
      expect(() => library.setValidationAdapter(zodAdapter)).not.toThrow();
    });

    test('should throw error when defining route without framework adapter', () => {
      library.setValidationAdapter(zodAdapter);

      const handler = defineHandler({
        handler: async () => ({ message: 'test' })
      });

      expect(() => {
        library.defineRoute('get', '/test', handler);
      }).toThrow('Framework adapter not set');
    });

    test('should throw error when defining route without validation adapter', () => {
      library.setFrameworkAdapter(expressAdapter);

      const handler = defineHandler({
        handler: async () => ({ message: 'test' })
      });

      expect(() => {
        library.defineRoute('get', '/test', handler);
      }).toThrow('Validation adapter not set');
    });
  });

  describe('Route Definition', () => {
    beforeEach(() => {
      library.setFrameworkAdapter(expressAdapter);
      library.setValidationAdapter(zodAdapter);
    });

    test('should define route without schema', () => {
      const handler = defineHandler({
        handler: async () => ({ message: 'test' })
      });

      expect(() => {
        library.defineRoute('get', '/test', handler);
      }).not.toThrow();
    });

    test('should define route with schema', () => {
      const handler = defineHandler({
        body: z.object({
          name: z.string()
        }),
        handler: async ({ body }) => ({ message: `Hello ${body.name}` })
      });

      expect(() => {
        library.defineRoute('post', '/test', handler);
      }).not.toThrow();
    });

    test('should define route with metadata', () => {
      const handler = defineHandler({
        metadata: {
          name: 'Test Route',
          description: 'A test route',
          tags: ['test']
        },
        handler: async () => ({ message: 'test' })
      });

      expect(() => {
        library.defineRoute('get', '/test', handler, undefined, {
          metadata: {
            name: 'Test Route',
            description: 'A test route'
          }
        });
      }).not.toThrow();
    });
  });

  describe('OpenAPI Generation', () => {
    beforeEach(() => {
      library.setFrameworkAdapter(expressAdapter);
      library.setValidationAdapter(zodAdapter);
    });

    test('should generate OpenAPI spec', () => {
      const spec = library.getOpenAPISpec();

      expect(spec).toHaveProperty('openapi', '3.0.0');
      expect(spec).toHaveProperty('info');
      expect(spec.info).toHaveProperty('title', 'Test API');
      expect(spec.info).toHaveProperty('version', '1.0.0');
      expect(spec).toHaveProperty('paths');
    });

    test('should include routes in OpenAPI spec', () => {
      const handler = defineHandler({
        body: z.object({
          name: z.string()
        }),
        response: z.object({
          message: z.string()
        }),
        handler: async ({ body }: any) => ({ message: `Hello ${body.name}` })
      });

      library.defineRoute('post', '/test', handler);

      const spec = library.getOpenAPISpec();
      expect(spec.paths).toHaveProperty('/test');
      expect(spec.paths['/test']).toHaveProperty('post');
    });
  });

  describe('Configuration', () => {
    test('should return current configuration', () => {
      const config = library.getConfig();

      expect(config).toHaveProperty('openapi');
      expect(config).toHaveProperty('validation');
      expect(config).toHaveProperty('errorHandling');
    });

    test('should update configuration', () => {
      library.updateConfig({
        openapi: {
          title: 'Updated API'
        }
      });

      const config = library.getConfig();
      expect(config.openapi?.title).toBe('Updated API');
    });
  });

  describe('Framework Instance Access', () => {
    test('should throw error when getting instance without adapter', () => {
      expect(() => {
        library.getFrameworkInstance();
      }).toThrow('Framework adapter not set');
    });

    test('should return framework instance when adapter is set', () => {
      library.setFrameworkAdapter(expressAdapter);

      const instance = library.getFrameworkInstance();
      expect(instance).toBeDefined();
      expect(typeof instance.use).toBe('function'); // Express app has 'use' method
    });
  });

  describe('Error Handling Setup', () => {
    test('should throw error when setting up error handling without adapter', () => {
      expect(() => {
        library.setupErrorHandling();
      }).toThrow('Framework adapter not set');
    });

    test('should setup error handling with adapter', () => {
      library.setFrameworkAdapter(expressAdapter);

      expect(() => {
        library.setupErrorHandling();
      }).not.toThrow();
    });

    test('should setup error handling with custom handler', () => {
      library.setFrameworkAdapter(expressAdapter);

      const customHandler = (error: any) => ({
        error: 'Custom Error',
        message: error.message,
        statusCode: 500
      });

      expect(() => {
        library.setupErrorHandling(customHandler);
      }).not.toThrow();
    });
  });
});
