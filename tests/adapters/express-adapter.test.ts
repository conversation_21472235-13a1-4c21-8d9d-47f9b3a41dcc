/**
 * Tests for ExpressAdapter
 */

import { Application } from 'express';
import request from 'supertest';
import { z } from 'zod';
import { ExpressAdapter, ExpressValidationError } from '../../src/adapters/express/express-adapter';
import { ZodAdapter } from '../../src/adapters/zod/zod-adapter';

describe('ExpressAdapter', () => {
  let adapter: ExpressAdapter;
  let app: Application;
  let zodAdapter: ZodAdapter;

  beforeEach(() => {
    adapter = new ExpressAdapter();
    app = adapter.getInstance();
    zodAdapter = new ZodAdapter();
  });

  describe('Basic Functionality', () => {
    test('should return correct name', () => {
      expect(adapter.getName()).toBe('express');
    });

    test('should return Express app instance', () => {
      const instance = adapter.getInstance();
      expect(instance).toBeDefined();
      expect(typeof instance.use).toBe('function');
      expect(typeof instance.get).toBe('function');
      expect(typeof instance.post).toBe('function');
    });

    test('should extract request data correctly', () => {
      const mockReq = {
        body: { name: 'John' },
        query: { page: '1' },
        params: { id: '123' },
        headers: { 'content-type': 'application/json' }
      } as any;

      const requestData = adapter.extractRequestData(mockReq);

      expect(requestData).toEqual({
        body: { name: 'John' },
        query: { page: '1' },
        params: { id: '123' },
        headers: { 'content-type': 'application/json' }
      });
    });
  });

  describe('Route Registration', () => {
    test('should register GET route', async () => {
      const handler = async () => ({ message: 'Hello World' });

      adapter.registerRoute('get', '/test', handler);

      const response = await request(app).get('/test');
      expect(response.status).toBe(200);
      expect(response.body).toEqual({ message: 'Hello World' });
    });

    test('should register POST route', async () => {
      const handler = async ({ body }: any) => ({ received: body });

      adapter.registerRoute('post', '/test', handler);

      const response = await request(app)
        .post('/test')
        .send({ name: 'John' });

      expect(response.status).toBe(200);
      expect(response.body).toEqual({ received: { name: 'John' } });
    });

    test('should handle async errors', async () => {
      const handler = async () => {
        throw new Error('Test error');
      };

      adapter.registerRoute('get', '/error', handler);

      // Add error handler
      app.use(adapter.createErrorHandler());

      const response = await request(app).get('/error');
      expect(response.status).toBe(500);
      expect(response.body).toHaveProperty('error');
    });
  });

  describe('Validation Middleware', () => {
    test('should validate request body', async () => {
      const schema = {
        body: z.object({
          name: z.string(),
          age: z.number()
        })
      };

      const validationMiddleware = adapter.createValidationMiddleware(schema, zodAdapter);
      const handler = async ({ body }: any) => ({ validated: body });

      adapter.registerRoute('post', '/validate', handler, [validationMiddleware]);

      const response = await request(app)
        .post('/validate')
        .send({ name: 'John', age: 30 });

      expect(response.status).toBe(200);
      expect(response.body.validated).toEqual({ name: 'John', age: 30 });
    });

    test('should return validation error for invalid data', async () => {
      const schema = {
        body: z.object({
          name: z.string(),
          age: z.number()
        })
      };

      const validationMiddleware = adapter.createValidationMiddleware(schema, zodAdapter);
      const handler = async ({ body }: any) => ({ validated: body });

      adapter.registerRoute('post', '/validate', handler, [validationMiddleware]);
      app.use(adapter.createErrorHandler());

      const response = await request(app)
        .post('/validate')
        .send({ name: 'John', age: 'thirty' });

      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('error');
      expect(response.body.error).toBe('Validation Error');
    });

    test('should validate query parameters', async () => {
      const schema = {
        query: z.object({
          page: z.string().transform(val => parseInt(val, 10)),
          limit: z.string().transform(val => parseInt(val, 10))
        })
      };

      const validationMiddleware = adapter.createValidationMiddleware(schema, zodAdapter);
      const handler = async ({ query }: any) => ({ validated: query });

      adapter.registerRoute('get', '/validate', handler, [validationMiddleware]);

      const response = await request(app)
        .get('/validate')
        .query({ page: '1', limit: '10' });

      expect(response.status).toBe(200);
      expect(response.body.validated).toEqual({ page: 1, limit: 10 });
    });

    test('should validate path parameters', async () => {
      const schema = {
        params: z.object({
          id: z.string().uuid()
        })
      };

      const validationMiddleware = adapter.createValidationMiddleware(schema, zodAdapter);
      const handler = async ({ params }: any) => ({ validated: params });

      adapter.registerRoute('get', '/validate/:id', handler, [validationMiddleware]);

      const uuid = '123e4567-e89b-12d3-a456-************';
      const response = await request(app).get(`/validate/${uuid}`);

      expect(response.status).toBe(200);
      expect(response.body.validated).toEqual({ id: uuid });
    });
  });

  describe('Error Handling', () => {
    test('should handle validation errors', async () => {
      const errorHandler = adapter.createErrorHandler();

      // Simulate a validation error
      app.get('/test-error', (_req, _res, next) => {
        const error = new ExpressValidationError(
          'Test validation error',
          'body',
          [{ field: 'name', message: 'Required', value: undefined, path: ['name'] }]
        );
        next(error);
      });

      app.use(errorHandler);

      const response = await request(app).get('/test-error');

      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('error', 'Validation Error');
      expect(response.body).toHaveProperty('details');
    });

    test('should handle generic errors', async () => {
      const errorHandler = adapter.createErrorHandler();

      app.get('/test-error', (_req, _res, next) => {
        next(new Error('Generic error'));
      });

      app.use(errorHandler);

      const response = await request(app).get('/test-error');

      expect(response.status).toBe(500);
      expect(response.body).toHaveProperty('error', 'Internal Server Error');
    });

    test('should use custom error handler', async () => {
      const customHandler = (_error: any) => ({
        error: 'Custom Error',
        message: 'Custom message',
        statusCode: 418
      });

      const errorHandler = adapter.createErrorHandler(customHandler);

      app.get('/test-error', (_req, _res, next) => {
        next(new Error('Test error'));
      });

      app.use(errorHandler);

      const response = await request(app).get('/test-error');

      expect(response.status).toBe(418);
      expect(response.body).toHaveProperty('error', 'Custom Error');
      expect(response.body).toHaveProperty('message', 'Custom message');
    });
  });

  describe('Response Handling', () => {
    test('should send JSON response', () => {
      const mockRes = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn(),
        setHeader: jest.fn(),
        end: jest.fn()
      } as any;

      adapter.sendResponse(mockRes, {
        status: 201,
        body: { message: 'Created' }
      });

      expect(mockRes.status).toHaveBeenCalledWith(201);
      expect(mockRes.json).toHaveBeenCalledWith({ message: 'Created' });
    });

    test('should send response with headers', () => {
      const mockRes = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn(),
        setHeader: jest.fn(),
        end: jest.fn()
      } as any;

      adapter.sendResponse(mockRes, {
        status: 200,
        body: { data: 'test' },
        headers: { 'X-Custom': 'value' }
      });

      expect(mockRes.setHeader).toHaveBeenCalledWith('X-Custom', 'value');
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({ data: 'test' });
    });

    test('should send empty response', () => {
      const mockRes = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn(),
        setHeader: jest.fn(),
        end: jest.fn()
      } as any;

      adapter.sendResponse(mockRes, { status: 204 });

      expect(mockRes.status).toHaveBeenCalledWith(204);
      expect(mockRes.end).toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });
});
