/**
 * Tests for ZodAdapter
 */

import { z } from 'zod';
import { ZodAdapter } from '../../src/adapters/zod/zod-adapter';

describe('ZodAdapter', () => {
  let adapter: ZodAdapter;

  beforeEach(() => {
    adapter = new ZodAdapter();
  });

  describe('Basic Functionality', () => {
    test('should return correct name', () => {
      expect(adapter.getName()).toBe('zod');
    });

    test('should validate schema correctly', () => {
      const schema = z.object({
        name: z.string(),
        age: z.number()
      });

      const validData = { name: '<PERSON>', age: 30 };
      const result = adapter.validate(validData, schema);

      expect(result).toEqual(validData);
    });

    test('should throw error for invalid data', () => {
      const schema = z.object({
        name: z.string(),
        age: z.number()
      });

      const invalidData = { name: '<PERSON>', age: 'thirty' };

      expect(() => {
        adapter.validate(invalidData, schema);
      }).toThrow();
    });

    test('should identify valid Zod schemas', () => {
      const validSchema = z.string();
      const invalidSchema = { type: 'string' };

      expect(adapter.isValidSchema(validSchema)).toBe(true);
      expect(adapter.isValidSchema(invalidSchema)).toBe(false);
    });
  });

  describe('Error Parsing', () => {
    test('should parse Zod validation errors', () => {
      const schema = z.object({
        name: z.string(),
        age: z.number().min(18)
      });

      try {
        schema.parse({ name: 123, age: 15 });
      } catch (error) {
        const parsedErrors = adapter.parseValidationError(error);

        expect(parsedErrors).toHaveLength(2);
        expect(parsedErrors[0]).toHaveProperty('field');
        expect(parsedErrors[0]).toHaveProperty('message');
        expect(parsedErrors[0]).toHaveProperty('path');
      }
    });

    test('should handle non-Zod errors', () => {
      const error = new Error('Generic error');
      const parsedErrors = adapter.parseValidationError(error);

      expect(parsedErrors).toHaveLength(1);
      expect(parsedErrors[0].field).toBe('unknown');
      expect(parsedErrors[0].message).toBe('Generic error');
    });
  });

  describe('OpenAPI Schema Conversion', () => {
    test('should convert string schema', () => {
      const schema = z.string();
      const openApiSchema = adapter.schemaToOpenAPI(schema);

      expect(openApiSchema).toEqual({ type: 'string' });
    });

    test('should convert string schema with constraints', () => {
      const schema = z.string().min(5).max(10).email();
      const openApiSchema = adapter.schemaToOpenAPI(schema);

      expect(openApiSchema).toEqual({
        type: 'string',
        minLength: 5,
        maxLength: 10,
        format: 'email'
      });
    });

    test('should convert number schema', () => {
      const schema = z.number();
      const openApiSchema = adapter.schemaToOpenAPI(schema);

      expect(openApiSchema).toEqual({ type: 'number' });
    });

    test('should convert number schema with constraints', () => {
      const schema = z.number().min(0).max(100).int();
      const openApiSchema = adapter.schemaToOpenAPI(schema);

      expect(openApiSchema).toEqual({
        type: 'integer',
        minimum: 0,
        maximum: 100
      });
    });

    test('should convert boolean schema', () => {
      const schema = z.boolean();
      const openApiSchema = adapter.schemaToOpenAPI(schema);

      expect(openApiSchema).toEqual({ type: 'boolean' });
    });

    test('should convert object schema', () => {
      const schema = z.object({
        name: z.string(),
        age: z.number(),
        email: z.string().optional()
      });

      const openApiSchema = adapter.schemaToOpenAPI(schema);

      expect(openApiSchema).toEqual({
        type: 'object',
        properties: {
          name: { type: 'string' },
          age: { type: 'number' },
          email: { type: 'string', nullable: false }
        },
        required: ['name', 'age']
      });
    });

    test('should convert array schema', () => {
      const schema = z.array(z.string());
      const openApiSchema = adapter.schemaToOpenAPI(schema);

      expect(openApiSchema).toEqual({
        type: 'array',
        items: { type: 'string' }
      });
    });

    test('should convert enum schema', () => {
      const schema = z.enum(['red', 'green', 'blue']);
      const openApiSchema = adapter.schemaToOpenAPI(schema);

      expect(openApiSchema).toEqual({
        type: 'string',
        enum: ['red', 'green', 'blue']
      });
    });

    test('should convert optional schema', () => {
      const schema = z.string().optional();
      const openApiSchema = adapter.schemaToOpenAPI(schema);

      expect(openApiSchema).toEqual({
        type: 'string',
        nullable: false
      });
    });

    test('should convert nullable schema', () => {
      const schema = z.string().nullable();
      const openApiSchema = adapter.schemaToOpenAPI(schema);

      expect(openApiSchema).toEqual({
        type: 'string',
        nullable: true
      });
    });

    test('should convert union schema', () => {
      const schema = z.union([z.string(), z.number()]);
      const openApiSchema = adapter.schemaToOpenAPI(schema);

      expect(openApiSchema).toEqual({
        oneOf: [
          { type: 'string' },
          { type: 'number' }
        ]
      });
    });

    test('should handle complex nested schemas', () => {
      const schema = z.object({
        user: z.object({
          name: z.string(),
          contacts: z.array(z.object({
            type: z.enum(['email', 'phone']),
            value: z.string()
          }))
        }),
        metadata: z.record(z.string())
      });

      const openApiSchema = adapter.schemaToOpenAPI(schema);

      expect(openApiSchema.type).toBe('object');
      expect(openApiSchema.properties.user.type).toBe('object');
      expect(openApiSchema.properties.user.properties.contacts.type).toBe('array');
      expect(openApiSchema.properties.metadata.type).toBe('object');
      expect(openApiSchema.properties.metadata.additionalProperties).toEqual({ type: 'string' });
    });
  });
});
