/**
 * Zod Validation Adapter
 * Implements the IValidationAdapter interface for Zod
 */

import { z, ZodSchema, ZodError, ZodType } from 'zod';
import { IValidationAdapter } from '../../core/interfaces';
import { ValidationError } from '../../core/types';

/**
 * Zod adapter implementation
 */
export class ZodAdapter implements IValidationAdapter<ZodSchema> {

  /**
   * Validate data against a Zod schema
   */
  validate(data: any, schema: ZodSchema): any {
    return schema.parse(data);
  }

  /**
   * Parse Zod validation errors into standardized format
   */
  parseValidationError(error: any): ValidationError[] {
    if (!(error instanceof ZodError)) {
      return [{
        field: 'unknown',
        message: error.message || 'Unknown validation error',
        value: undefined,
        path: []
      }];
    }

    return error.errors.map(zodError => ({
      field: zodError.path.join('.') || 'root',
      message: zodError.message,
      value: (zodError as any).received,
      path: zodError.path.map(p => String(p))
    }));
  }

  /**
   * Convert Zod schema to OpenAPI specification format
   */
  schemaToOpenAPI(schema: ZodSchema): any {
    return this.zodToOpenAPI(schema);
  }

  /**
   * Check if a value is a valid Zod schema
   */
  isValidSchema(schema: any): schema is ZodSchema {
    return schema instanceof ZodType;
  }

  /**
   * Get the adapter name
   */
  getName(): string {
    return 'zod';
  }

  /**
   * Convert Zod schema to OpenAPI specification
   * This is a comprehensive implementation covering most Zod types
   */
  private zodToOpenAPI(schema: ZodSchema): any {
    // Handle ZodOptional and ZodNullable wrappers
    if (schema instanceof z.ZodOptional) {
      const innerSchema = this.zodToOpenAPI(schema._def.innerType);
      return { ...innerSchema, nullable: false };
    }

    if (schema instanceof z.ZodNullable) {
      const innerSchema = this.zodToOpenAPI(schema._def.innerType);
      return { ...innerSchema, nullable: true };
    }

    // Handle ZodDefault
    if (schema instanceof z.ZodDefault) {
      const innerSchema = this.zodToOpenAPI(schema._def.innerType);
      return { ...innerSchema, default: schema._def.defaultValue() };
    }

    // Basic types
    if (schema instanceof z.ZodString) {
      const result: any = { type: 'string' };

      // Handle string constraints
      if (schema._def.checks) {
        for (const check of schema._def.checks) {
          switch (check.kind) {
            case 'min':
              result.minLength = check.value;
              break;
            case 'max':
              result.maxLength = check.value;
              break;
            case 'email':
              result.format = 'email';
              break;
            case 'url':
              result.format = 'uri';
              break;
            case 'uuid':
              result.format = 'uuid';
              break;
            case 'regex':
              result.pattern = check.regex.source;
              break;
          }
        }
      }

      return result;
    }

    if (schema instanceof z.ZodNumber) {
      const result: any = { type: 'number' };

      // Handle number constraints
      if (schema._def.checks) {
        for (const check of schema._def.checks) {
          switch (check.kind) {
            case 'min':
              result.minimum = check.value;
              if (!check.inclusive) result.exclusiveMinimum = true;
              break;
            case 'max':
              result.maximum = check.value;
              if (!check.inclusive) result.exclusiveMaximum = true;
              break;
            case 'int':
              result.type = 'integer';
              break;
          }
        }
      }

      return result;
    }

    if (schema instanceof z.ZodBoolean) {
      return { type: 'boolean' };
    }

    if (schema instanceof z.ZodDate) {
      return { type: 'string', format: 'date-time' };
    }

    // Complex types
    if (schema instanceof z.ZodObject) {
      const properties: Record<string, any> = {};
      const required: string[] = [];
      const shape = schema._def.shape();

      for (const [key, value] of Object.entries(shape)) {
        const propertySchema = this.zodToOpenAPI(value as ZodSchema);
        properties[key] = propertySchema;

        // Check if property is required (not optional)
        if (!(value instanceof z.ZodOptional)) {
          required.push(key);
        }
      }

      const result: any = {
        type: 'object',
        properties
      };

      if (required.length > 0) {
        result.required = required;
      }

      return result;
    }

    if (schema instanceof z.ZodArray) {
      return {
        type: 'array',
        items: this.zodToOpenAPI(schema._def.type)
      };
    }

    if (schema instanceof z.ZodEnum) {
      return {
        type: 'string',
        enum: schema._def.values
      };
    }

    if (schema instanceof z.ZodLiteral) {
      const value = schema._def.value;
      return {
        type: typeof value,
        enum: [value]
      };
    }

    if (schema instanceof z.ZodUnion) {
      return {
        oneOf: schema._def.options.map((option: ZodSchema) => this.zodToOpenAPI(option))
      };
    }

    if (schema instanceof z.ZodIntersection) {
      return {
        allOf: [
          this.zodToOpenAPI(schema._def.left),
          this.zodToOpenAPI(schema._def.right)
        ]
      };
    }

    if (schema instanceof z.ZodRecord) {
      return {
        type: 'object',
        additionalProperties: this.zodToOpenAPI(schema._def.valueType)
      };
    }

    if (schema instanceof z.ZodTuple) {
      return {
        type: 'array',
        items: schema._def.items.map((item: ZodSchema) => this.zodToOpenAPI(item)),
        minItems: schema._def.items.length,
        maxItems: schema._def.items.length
      };
    }

    // Fallback for unknown types
    return { type: 'object' };
  }
}
