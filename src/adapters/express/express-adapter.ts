/**
 * Express.js Framework Adapter
 * Implements the IFrameworkAdapter interface for Express.js
 */

import express, { Application, Request, Response, NextFunction, RequestHandler } from 'express';
import {
  IFrameworkAdapter,
  IValidationAdapter
} from '../../core/interfaces';
import {
  HttpMethod,
  RequestData,
  ResponseData,
  RouteSchema,
  HandlerFunction,
  ErrorResponse,
  ValidationError
} from '../../core/types';

/**
 * Express-specific validation error class
 */
export class ExpressValidationError extends Error {
  constructor(
    message: string,
    public field: string,
    public validationErrors: ValidationError[]
  ) {
    super(message);
    this.name = 'ExpressValidationError';
  }
}

/**
 * Express.js adapter implementation
 */
export class ExpressAdapter implements IFrameworkAdapter {
  private app: Application;

  constructor(app?: Application) {
    this.app = app || express();

    // Set up basic middleware
    this.app.use(express.json());
    this.app.use(express.urlencoded({ extended: true }));
  }

  /**
   * Register a route with Express
   */
  registerRoute(
    method: HttpMethod,
    path: string,
    handler: HandlerFunction,
    middleware: RequestHandler[] = []
  ): void {
    const expressHandler: RequestHandler = async (req: Request, res: Response, next: NextFunction) => {
      try {
        // Extract validated data from request (set by validation middleware)
        const validated = (req as any).validated || this.extractRequestData(req);

        // Call the handler
        const result = await handler(validated, { req, res });

        // Send response
        if (!res.headersSent) {
          this.sendResponse(res, { body: result });
        }
      } catch (error) {
        next(error);
      }
    };

    // Register the route with middleware
    const allMiddleware = [...middleware, expressHandler];
    this.app[method](path, ...allMiddleware);
  }

  /**
   * Create validation middleware for Express
   */
  createValidationMiddleware(
    schema: RouteSchema,
    validationAdapter: IValidationAdapter
  ): RequestHandler {
    return async (req: Request, _res: Response, next: NextFunction) => {
      try {
        const validated: any = {};

        // Validate body
        if (schema.body) {
          try {
            validated.body = await validationAdapter.validate(req.body, schema.body);
          } catch (error) {
            const validationErrors = validationAdapter.parseValidationError(error);
            throw new ExpressValidationError('Body validation failed', 'body', validationErrors);
          }
        }

        // Validate query parameters
        if (schema.query) {
          try {
            validated.query = await validationAdapter.validate(req.query, schema.query);
          } catch (error) {
            const validationErrors = validationAdapter.parseValidationError(error);
            throw new ExpressValidationError('Query validation failed', 'query', validationErrors);
          }
        }

        // Validate path parameters
        if (schema.params) {
          try {
            validated.params = await validationAdapter.validate(req.params, schema.params);
          } catch (error) {
            const validationErrors = validationAdapter.parseValidationError(error);
            throw new ExpressValidationError('Params validation failed', 'params', validationErrors);
          }
        }

        // Attach validated data to request
        (req as any).validated = validated;
        next();
      } catch (error) {
        next(error);
      }
    };
  }

  /**
   * Create error handling middleware for Express
   */
  createErrorHandler(customHandler?: (error: any) => ErrorResponse): any {
    return (error: any, _req: Request, res: Response, next: NextFunction) => {
      // If response already sent, delegate to default Express error handler
      if (res.headersSent) {
        return next(error);
      }

      let errorResponse: ErrorResponse;

      if (customHandler) {
        errorResponse = customHandler(error);
      } else {
        // Default error handling
        if (error instanceof ExpressValidationError) {
          errorResponse = {
            error: 'Validation Error',
            message: error.message,
            details: error.validationErrors,
            statusCode: 400
          };
        } else {
          errorResponse = {
            error: 'Internal Server Error',
            message: process.env.NODE_ENV === 'development' ? error.message : 'Something went wrong',
            statusCode: 500
          };
        }
      }

      res.status(errorResponse.statusCode || 500).json(errorResponse);
    };
  }

  /**
   * Extract request data from Express request object
   */
  extractRequestData(request: Request): RequestData {
    return {
      body: request.body,
      query: request.query,
      params: request.params,
      headers: request.headers as Record<string, string>
    };
  }

  /**
   * Send response using Express response object
   */
  sendResponse(response: Response, data: ResponseData): void {
    if (data.headers) {
      Object.entries(data.headers).forEach(([key, value]) => {
        response.setHeader(key, value);
      });
    }

    const status = data.status || 200;

    if (data.body !== undefined) {
      response.status(status).json(data.body);
    } else {
      response.status(status).end();
    }
  }

  /**
   * Get the Express application instance
   */
  getInstance(): Application {
    return this.app;
  }

  /**
   * Get the adapter name
   */
  getName(): string {
    return 'express';
  }

  /**
   * Start the Express server
   */
  listen(port: number, callback?: () => void): void {
    this.app.listen(port, callback);
  }
}
