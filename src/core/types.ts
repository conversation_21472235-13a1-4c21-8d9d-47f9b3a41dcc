/**
 * Core types for the Express Plus library
 * These types are framework and validation library agnostic
 */

// HTTP Methods supported by the library
export type HttpMethod = 'get' | 'post' | 'put' | 'patch' | 'delete' | 'head' | 'options';

// Generic request data structure
export interface RequestData {
  body?: any;
  query?: any;
  params?: any;
  headers?: Record<string, string>;
}

// Generic response data structure
export interface ResponseData {
  status?: number;
  body?: any;
  headers?: Record<string, string>;
}

// Schema definition that can be used with any validation library
export interface RouteSchema<TSchema = any> {
  body?: TSchema;
  query?: TSchema;
  params?: TSchema;
  response?: TSchema;
}

// Handler function type that receives validated data
export type HandlerFunction<TValidated = RequestData, TResponse = any> = (
  validated: TValidated,
  context?: any
) => Promise<TResponse> | TResponse;

// Enhanced handler with attached schema metadata
export interface EnhancedHandler<TSchema = any, TValidated = RequestData, TResponse = any> 
  extends HandlerFunction<TValidated, TResponse> {
  __schema?: RouteSchema<TSchema>;
  __metadata?: {
    name?: string;
    description?: string;
    tags?: string[];
  };
}

// Validation error structure
export interface ValidationError {
  field: string;
  message: string;
  value?: any;
  path?: string[];
}

// Generic error response
export interface ErrorResponse {
  error: string;
  message: string;
  details?: ValidationError[];
  statusCode?: number;
}

// OpenAPI specification structure
export interface OpenAPISpec {
  openapi: string;
  info: {
    title: string;
    version: string;
    description?: string;
  };
  paths: Record<string, any>;
  components?: {
    schemas?: Record<string, any>;
    responses?: Record<string, any>;
    parameters?: Record<string, any>;
  };
}

// Route registration options
export interface RouteOptions<TSchema = any> {
  method: HttpMethod;
  path: string;
  handler: EnhancedHandler<TSchema>;
  schema?: RouteSchema<TSchema>;
  middleware?: any[];
  metadata?: {
    name?: string;
    description?: string;
    tags?: string[];
  };
}

// Library configuration
export interface LibraryConfig {
  openapi?: {
    title?: string;
    version?: string;
    description?: string;
  };
  validation?: {
    stripUnknown?: boolean;
    abortEarly?: boolean;
  };
  errorHandling?: {
    includeStack?: boolean;
    customErrorHandler?: (error: any) => ErrorResponse;
  };
}
