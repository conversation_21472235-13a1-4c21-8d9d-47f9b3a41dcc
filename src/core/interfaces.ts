/**
 * Core interfaces for the adapter pattern
 * These interfaces define the contracts that framework and validation adapters must implement
 */

import { 
  HttpMethod, 
  RequestData, 
  ResponseData, 
  RouteSchema, 
  HandlerFunction, 
  ValidationError,
  ErrorResponse,
  OpenAPISpec
} from './types';

/**
 * Abstract interface for validation adapters
 * Allows swapping between different validation libraries (<PERSON>od, Joi, etc.)
 */
export interface IValidationAdapter<TSchema = any> {
  /**
   * Validate request data against a schema
   */
  validate(data: any, schema: TSchema): Promise<any> | any;
  
  /**
   * Parse validation errors into a standardized format
   */
  parseValidationError(error: any): ValidationError[];
  
  /**
   * Convert schema to OpenAPI specification format
   */
  schemaToOpenAPI(schema: TSchema): any;
  
  /**
   * Check if a value is a valid schema for this adapter
   */
  isValidSchema(schema: any): schema is TSchema;
  
  /**
   * Get the name/identifier of this validation adapter
   */
  getName(): string;
}

/**
 * Abstract interface for framework adapters
 * Allows swapping between different web frameworks (Express, Fastify, Hono, etc.)
 */
export interface IFrameworkAdapter {
  /**
   * Register a route with the framework
   */
  registerRoute(
    method: HttpMethod,
    path: string,
    handler: HandlerFunction,
    middleware?: any[]
  ): void;
  
  /**
   * Create middleware for request validation
   */
  createValidationMiddleware(
    schema: RouteSchema,
    validationAdapter: IValidationAdapter
  ): any;
  
  /**
   * Create error handling middleware
   */
  createErrorHandler(customHandler?: (error: any) => ErrorResponse): any;
  
  /**
   * Extract request data from framework-specific request object
   */
  extractRequestData(request: any): RequestData;
  
  /**
   * Send response using framework-specific response object
   */
  sendResponse(response: any, data: ResponseData): void;
  
  /**
   * Get the underlying framework instance
   */
  getInstance(): any;
  
  /**
   * Get the name/identifier of this framework adapter
   */
  getName(): string;
  
  /**
   * Start the server (if applicable)
   */
  listen(port: number, callback?: () => void): void;
}

/**
 * Interface for OpenAPI documentation generator
 */
export interface IOpenAPIGenerator {
  /**
   * Add a route to the OpenAPI specification
   */
  addRoute(
    path: string,
    method: HttpMethod,
    schema: RouteSchema,
    validationAdapter: IValidationAdapter,
    metadata?: {
      name?: string;
      description?: string;
      tags?: string[];
    }
  ): void;
  
  /**
   * Get the complete OpenAPI specification
   */
  getSpec(): OpenAPISpec;
  
  /**
   * Update the API info
   */
  updateInfo(info: { title?: string; version?: string; description?: string }): void;
  
  /**
   * Clear all routes from the specification
   */
  clear(): void;
}

/**
 * Main library interface that orchestrates all adapters
 */
export interface IExpressPlusLibrary {
  /**
   * Set the framework adapter
   */
  setFrameworkAdapter(adapter: IFrameworkAdapter): void;
  
  /**
   * Set the validation adapter
   */
  setValidationAdapter(adapter: IValidationAdapter): void;
  
  /**
   * Register a route
   */
  defineRoute(
    method: HttpMethod,
    path: string,
    handler: HandlerFunction,
    schema?: RouteSchema,
    options?: {
      middleware?: any[];
      metadata?: {
        name?: string;
        description?: string;
        tags?: string[];
      };
    }
  ): void;
  
  /**
   * Get the OpenAPI specification
   */
  getOpenAPISpec(): OpenAPISpec;
  
  /**
   * Get the framework instance
   */
  getFrameworkInstance(): any;
  
  /**
   * Initialize error handling
   */
  setupErrorHandling(customHandler?: (error: any) => ErrorResponse): void;
}
