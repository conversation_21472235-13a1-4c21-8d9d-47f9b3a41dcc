/**
 * OpenAPI Documentation Generator
 * Generates OpenAPI 3.0 specifications from route definitions
 */

import { IOpenAP<PERSON>enerator, IValidationAdapter } from './interfaces';
import { HttpMethod, RouteSchema, OpenAPISpec } from './types';

/**
 * OpenAPI generator implementation
 */
export class OpenAPIGenerator implements IOpenAPIGenerator {
  private spec: OpenAPISpec;

  constructor(info?: { title?: string; version?: string; description?: string }) {
    this.spec = {
      openapi: '3.0.0',
      info: {
        title: info?.title || 'Express Plus API',
        version: info?.version || '1.0.0',
        description: info?.description || 'API generated with Express Plus'
      },
      paths: {},
      components: {
        schemas: {},
        responses: {},
        parameters: {}
      }
    };
  }

  /**
   * Add a route to the OpenAPI specification
   */
  addRoute(
    path: string,
    method: HttpMethod,
    schema: RouteSchema,
    validationAdapter: IValidationAdapter,
    metadata?: {
      name?: string;
      description?: string;
      tags?: string[];
    }
  ): void {
    // Initialize path if it doesn't exist
    if (!this.spec.paths[path]) {
      this.spec.paths[path] = {};
    }

    // Create operation object
    const operation: any = {
      summary: metadata?.name || `${method.toUpperCase()} ${path}`,
      description: metadata?.description || `${method.toUpperCase()} operation for ${path}`,
      responses: {
        '200': {
          description: 'Success'
        },
        '400': {
          description: 'Bad Request',
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  error: { type: 'string' },
                  message: { type: 'string' },
                  details: {
                    type: 'array',
                    items: {
                      type: 'object',
                      properties: {
                        field: { type: 'string' },
                        message: { type: 'string' },
                        value: {},
                        path: {
                          type: 'array',
                          items: { type: 'string' }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        },
        '500': {
          description: 'Internal Server Error',
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  error: { type: 'string' },
                  message: { type: 'string' }
                }
              }
            }
          }
        }
      }
    };

    // Add tags if provided
    if (metadata?.tags && metadata.tags.length > 0) {
      operation.tags = metadata.tags;
    }

    // Handle request body
    if (schema.body && validationAdapter.isValidSchema(schema.body)) {
      operation.requestBody = {
        required: true,
        content: {
          'application/json': {
            schema: validationAdapter.schemaToOpenAPI(schema.body)
          }
        }
      };
    }

    // Handle parameters (query and path)
    operation.parameters = [];

    // Add query parameters
    if (schema.query && validationAdapter.isValidSchema(schema.query)) {
      const querySchema = validationAdapter.schemaToOpenAPI(schema.query);
      
      if (querySchema.type === 'object' && querySchema.properties) {
        for (const [paramName, paramSchema] of Object.entries(querySchema.properties)) {
          operation.parameters.push({
            name: paramName,
            in: 'query',
            required: querySchema.required?.includes(paramName) || false,
            schema: paramSchema
          });
        }
      }
    }

    // Add path parameters
    if (schema.params && validationAdapter.isValidSchema(schema.params)) {
      const paramsSchema = validationAdapter.schemaToOpenAPI(schema.params);
      
      if (paramsSchema.type === 'object' && paramsSchema.properties) {
        for (const [paramName, paramSchema] of Object.entries(paramsSchema.properties)) {
          operation.parameters.push({
            name: paramName,
            in: 'path',
            required: true,
            schema: paramSchema
          });
        }
      }
    }

    // Handle response schema
    if (schema.response && validationAdapter.isValidSchema(schema.response)) {
      operation.responses['200'] = {
        description: 'Success',
        content: {
          'application/json': {
            schema: validationAdapter.schemaToOpenAPI(schema.response)
          }
        }
      };
    }

    // Remove parameters array if empty
    if (operation.parameters.length === 0) {
      delete operation.parameters;
    }

    // Add operation to spec
    this.spec.paths[path][method] = operation;
  }

  /**
   * Get the complete OpenAPI specification
   */
  getSpec(): OpenAPISpec {
    return JSON.parse(JSON.stringify(this.spec)); // Deep clone
  }

  /**
   * Update the API info
   */
  updateInfo(info: { title?: string; version?: string; description?: string }): void {
    if (info.title) this.spec.info.title = info.title;
    if (info.version) this.spec.info.version = info.version;
    if (info.description) this.spec.info.description = info.description;
  }

  /**
   * Clear all routes from the specification
   */
  clear(): void {
    this.spec.paths = {};
    if (this.spec.components) {
      this.spec.components.schemas = {};
      this.spec.components.responses = {};
      this.spec.components.parameters = {};
    }
  }

  /**
   * Add a reusable schema component
   */
  addSchemaComponent(name: string, schema: any): void {
    if (!this.spec.components) {
      this.spec.components = {};
    }
    if (!this.spec.components.schemas) {
      this.spec.components.schemas = {};
    }
    this.spec.components.schemas[name] = schema;
  }

  /**
   * Get a reference to a schema component
   */
  getSchemaReference(name: string): any {
    return { $ref: `#/components/schemas/${name}` };
  }
}
