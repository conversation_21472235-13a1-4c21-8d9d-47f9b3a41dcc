/**
 * Main Express Plus Library
 * Orchestrates framework and validation adapters using the adapter pattern
 */

import {
  IExpressPlusLibrary,
  IFrameworkAdapter,
  IValidationAdapter,
  IOpenAPIGenerator
} from './interfaces';
import {
  HttpMethod,
  RouteSchema,
  HandlerFunction,
  EnhancedHandler,
  OpenAPISpec,
  ErrorResponse,
  LibraryConfig,
  RequestData
} from './types';
import { OpenAPIGenerator } from './openapi-generator';

/**
 * Utility function to create an enhanced handler with schema metadata
 */
export function defineHandler<TSchema = any, TValidated = RequestData, TResponse = any>(options: {
  handler: HandlerFunction<TValidated, TResponse>;
  body?: TSchema;
  query?: TSchema;
  params?: TSchema;
  response?: TSchema;
  metadata?: {
    name?: string;
    description?: string;
    tags?: string[];
  };
}): EnhancedHandler<TSchema, TValidated, TResponse> {
  const { handler, metadata, ...schema } = options;

  const enhancedHandler = handler as Enhanced<PERSON>andler<TSchema, TValidated, TResponse>;
  enhancedHandler.__schema = schema;
  enhancedHandler.__metadata = metadata;

  return enhancedHandler;
}

/**
 * Main library class that implements the adapter pattern
 */
export class ExpressPlusLibrary implements IExpressPlusLibrary {
  private frameworkAdapter?: IFrameworkAdapter;
  private validationAdapter?: IValidationAdapter;
  private openAPIGenerator: IOpenAPIGenerator;
  private config: LibraryConfig;

  constructor(config?: LibraryConfig) {
    this.config = {
      openapi: {
        title: 'Express Plus API',
        version: '1.0.0',
        description: 'API generated with Express Plus',
        ...config?.openapi
      },
      validation: {
        stripUnknown: true,
        abortEarly: false,
        ...config?.validation
      },
      errorHandling: {
        includeStack: process.env.NODE_ENV === 'development',
        ...config?.errorHandling
      }
    };

    this.openAPIGenerator = new OpenAPIGenerator(this.config.openapi);
  }

  /**
   * Set the framework adapter (Express, Fastify, etc.)
   */
  setFrameworkAdapter(adapter: IFrameworkAdapter): void {
    this.frameworkAdapter = adapter;
  }

  /**
   * Set the validation adapter (Zod, Joi, etc.)
   */
  setValidationAdapter(adapter: IValidationAdapter): void {
    this.validationAdapter = adapter;
  }

  /**
   * Register a route with validation and OpenAPI documentation
   */
  defineRoute(
    method: HttpMethod,
    path: string,
    handler: HandlerFunction,
    schema?: RouteSchema,
    options?: {
      middleware?: any[];
      metadata?: {
        name?: string;
        description?: string;
        tags?: string[];
      };
    }
  ): void {
    if (!this.frameworkAdapter) {
      throw new Error('Framework adapter not set. Call setFrameworkAdapter() first.');
    }

    if (!this.validationAdapter) {
      throw new Error('Validation adapter not set. Call setValidationAdapter() first.');
    }

    // Extract schema from enhanced handler if not provided
    const finalSchema = schema || (handler as EnhancedHandler).__schema || {};

    // Create validation middleware if schema is provided
    const middleware = options?.middleware || [];
    if (Object.keys(finalSchema).length > 0) {
      const validationMiddleware = this.frameworkAdapter.createValidationMiddleware(
        finalSchema,
        this.validationAdapter
      );
      middleware.unshift(validationMiddleware);
    }

    // Register the route with the framework
    this.frameworkAdapter.registerRoute(method, path, handler, middleware);

    // Add to OpenAPI documentation
    this.openAPIGenerator.addRoute(
      path,
      method,
      finalSchema,
      this.validationAdapter,
      options?.metadata
    );
  }

  /**
   * Get the OpenAPI specification
   */
  getOpenAPISpec(): OpenAPISpec {
    return this.openAPIGenerator.getSpec();
  }

  /**
   * Get the framework instance
   */
  getFrameworkInstance(): any {
    if (!this.frameworkAdapter) {
      throw new Error('Framework adapter not set. Call setFrameworkAdapter() first.');
    }
    return this.frameworkAdapter.getInstance();
  }

  /**
   * Setup error handling middleware
   */
  setupErrorHandling(customHandler?: (error: any) => ErrorResponse): void {
    if (!this.frameworkAdapter) {
      throw new Error('Framework adapter not set. Call setFrameworkAdapter() first.');
    }

    const errorHandler = this.frameworkAdapter.createErrorHandler(
      customHandler || this.createDefaultErrorHandler()
    );

    // For Express, we need to add the error handler to the app
    const app = this.frameworkAdapter.getInstance();
    if (app && typeof app.use === 'function') {
      app.use(errorHandler);
    }
  }

  /**
   * Create a default error handler
   */
  private createDefaultErrorHandler(): (error: any) => ErrorResponse {
    return (error: any): ErrorResponse => {
      const includeStack = this.config.errorHandling?.includeStack || false;

      // Default error response
      const errorResponse: ErrorResponse = {
        error: 'Internal Server Error',
        message: includeStack ? error.message : 'Something went wrong',
        statusCode: 500
      };

      // Add stack trace in development
      if (includeStack && error.stack) {
        (errorResponse as any).stack = error.stack;
      }

      return errorResponse;
    };
  }

  /**
   * Add an OpenAPI documentation endpoint
   */
  addOpenAPIEndpoint(path: string = '/docs/openapi.json'): void {
    if (!this.frameworkAdapter) {
      throw new Error('Framework adapter not set. Call setFrameworkAdapter() first.');
    }

    const handler: HandlerFunction = () => {
      return this.getOpenAPISpec();
    };

    this.frameworkAdapter.registerRoute('get', path, handler);
  }

  /**
   * Start the server (convenience method)
   */
  listen(port: number, callback?: () => void): void {
    if (!this.frameworkAdapter) {
      throw new Error('Framework adapter not set. Call setFrameworkAdapter() first.');
    }

    this.frameworkAdapter.listen(port, callback);
  }

  /**
   * Get the current configuration
   */
  getConfig(): LibraryConfig {
    return { ...this.config };
  }

  /**
   * Update the configuration
   */
  updateConfig(config: Partial<LibraryConfig>): void {
    this.config = {
      ...this.config,
      ...config,
      openapi: { ...this.config.openapi, ...config.openapi },
      validation: { ...this.config.validation, ...config.validation },
      errorHandling: { ...this.config.errorHandling, ...config.errorHandling }
    };

    // Update OpenAPI generator info
    if (config.openapi) {
      this.openAPIGenerator.updateInfo(config.openapi);
    }
  }
}
